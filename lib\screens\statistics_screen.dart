import 'package:flutter/material.dart';
import '../services/storage_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  Map<String, GameStats> gameStats = {};
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    final games = ['o_an_quan', 'co_ca_ngua', 'co_ganh'];
    
    for (String gameId in games) {
      gameStats[gameId] = await StorageService().getGameStats(gameId);
    }
    
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thống kê'),
        backgroundColor: AppColors.primaryRed,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.background, AppColors.creamWhite],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          children: [
            _buildOverallStats(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            _buildGameStats('Ô Ăn Quan', 'o_an_quan'),
            const SizedBox(height: AppConstants.paddingMedium),
            
            _buildGameStats('Cờ Cá Ngựa', 'co_ca_ngua'),
            const SizedBox(height: AppConstants.paddingMedium),
            
            _buildGameStats('Cờ Gánh', 'co_ganh'),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallStats() {
    int totalGames = 0;
    int totalWins = 0;
    int totalScore = 0;
    Duration totalTime = Duration.zero;

    for (GameStats stats in gameStats.values) {
      totalGames += stats.gamesPlayed;
      totalWins += stats.gamesWon;
      totalScore += stats.totalScore;
      totalTime += stats.totalPlayTime;
    }

    double overallWinRate = totalGames > 0 ? totalWins / totalGames : 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Tổng quan',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Tổng trận', 
                    totalGames.toString(),
                    Icons.games,
                    AppColors.textSecondary,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: _buildStatCard(
                    'Tỷ lệ thắng', 
                    '${(overallWinRate * 100).toStringAsFixed(1)}%',
                    Icons.trending_up,
                    AppColors.success,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingSmall),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Tổng điểm', 
                    totalScore.toString(),
                    Icons.star,
                    AppColors.warning,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: _buildStatCard(
                    'Thời gian chơi', 
                    _formatDuration(totalTime),
                    Icons.access_time,
                    AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameStats(String gameName, String gameId) {
    GameStats stats = gameStats[gameId] ?? GameStats();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getGameIcon(gameId),
                  color: AppColors.primaryRed,
                  size: 24,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Text(
                  gameName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            if (stats.gamesPlayed == 0)
              const Text(
                'Chưa chơi game này',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              )
            else ...[
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem('Trận đã chơi', stats.gamesPlayed.toString()),
                  ),
                  Expanded(
                    child: _buildStatItem('Thắng', stats.gamesWon.toString()),
                  ),
                  Expanded(
                    child: _buildStatItem('Thua', stats.gamesLost.toString()),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.paddingSmall),
              
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Tỷ lệ thắng', 
                      '${(stats.winRate * 100).toStringAsFixed(1)}%',
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem('Điểm cao nhất', stats.bestScore.toString()),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Điểm TB', 
                      stats.averageScore.toStringAsFixed(1),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.paddingSmall),
              
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Thời gian chơi', 
                      _formatDuration(stats.totalPlayTime),
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Lần cuối chơi', 
                      stats.lastPlayed != null 
                          ? _formatDate(stats.lastPlayed!)
                          : 'Chưa chơi',
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  IconData _getGameIcon(String gameId) {
    switch (gameId) {
      case 'o_an_quan':
        return Icons.circle_outlined;
      case 'co_ca_ngua':
        return Icons.casino;
      case 'co_ganh':
        return Icons.grid_on;
      default:
        return Icons.games;
    }
  }

  String _formatDuration(Duration duration) {
    int hours = duration.inHours;
    int minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  String _formatDate(DateTime date) {
    DateTime now = DateTime.now();
    Duration difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} ngày trước';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }
}
