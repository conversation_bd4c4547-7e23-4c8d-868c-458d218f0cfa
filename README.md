# traditional_game

A new Flutter project.

## Getting Started

# Dân Gian Trí <PERSON>ệ

Game tổng hợp các trò chơi dân gian truyền thống Việt Nam được phát triển bằng Flutter.

## 🎮 Các trò chơi

### 1. Ô Ăn Quan
- Tr<PERSON> chơi chiến thuật truyền thống với 12 ô dân và 2 ô quan
- AI thông minh với nhiều mức độ khó
- <PERSON><PERSON>t chơi đầy đủ và chính xác

### 2. <PERSON><PERSON> Cá Ngựa
- Tr<PERSON> chơi may rủi với xúc xắc
- 6 con vật: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ôm, Gà, Bầ<PERSON>, Na<PERSON>
- <PERSON><PERSON> thống đặt cược linh hoạt

### 3. Cờ Gánh
- Tr<PERSON> chơi chiến thuật trên bàn cờ 5x5
- <PERSON><PERSON> chế "gánh" độ<PERSON> đáo
- <PERSON> với thuật toán minimax

## ✨ T<PERSON>h năng

- **🎵 Âm thanh**: Hiệu ứng âm thanh và nhạc nền dân gian
- **🎨 Giao diện**: Theme truyền thống Việt Nam với màu sắc đặc trưng
- **🤖 AI thông minh**: 3 mức độ khó (Dễ, Trung bình, Khó)
- **📊 Thống kê**: Theo dõi thành tích và tiến bộ
- **⚙️ Cài đặt**: Tùy chỉnh âm thanh, độ khó, giao diện
- **💾 Lưu trữ**: Tự động lưu tiến trình và cài đặt
- **📱 Offline**: Chơi hoàn toàn offline, không cần mạng

## 🛠 Công nghệ

- **Framework**: Flutter 3.27.0
- **Ngôn ngữ**: Dart
- **Lưu trữ**: SharedPreferences
- **Âm thanh**: AudioPlayers
- **Animation**: Flutter Animate
- **Testing**: Flutter Test

## 📦 Cài đặt

1. Clone repository:
```bash
git clone https://github.com/your-username/traditional_game.git
cd traditional_game
```

2. Cài đặt dependencies:
```bash
flutter pub get
```

3. Chạy ứng dụng:
```bash
flutter run
```

## 🧪 Testing

Chạy tất cả test:
```bash
flutter test
```

Chạy test với coverage:
```bash
flutter test --coverage
```

## 📱 Build

### Android
```bash
flutter build apk --release
```

### iOS
```bash
flutter build ios --release
```

## 🎯 Roadmap

- [ ] Thêm trò chơi Cờ Tướng mini
- [ ] Thêm trò chơi Cờ Vây 9x9
- [ ] Multiplayer online
- [ ] Thêm nhiều theme giao diện
- [ ] Hệ thống achievement
- [ ] Leaderboard

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Vui lòng:

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 👥 Tác giả

- **Your Name** - *Initial work* - [YourGitHub](https://github.com/your-username)

## 🙏 Cảm ơn

- Cảm ơn cộng đồng Flutter Việt Nam
- Cảm ơn các nghệ nhân dân gian đã lưu giữ những trò chơi truyền thống
- Cảm ơn tất cả contributors
