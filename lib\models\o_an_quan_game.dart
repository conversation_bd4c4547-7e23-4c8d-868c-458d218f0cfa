enum Player { player1, player2 }

enum GameStatus { playing, gameOver }

class OAnQuanGame {
  // Bàn cờ có 10 ô dân (5 ô mỗi bên) + 2 ô quan
  List<int> board = List.filled(10, 5); // 10 ô dân, mỗi ô có 5 quân
  int quan1 = 10; // Quan bên trái (vị trí -1)
  int quan2 = 10; // Quan bên phải (vị trí -2)

  Player currentPlayer = Player.player1;
  GameStatus status = GameStatus.playing;

  int player1Score = 0;
  int player2Score = 0;

  // Constructor
  OAnQuanGame() {
    _initializeBoard();
  }

  void _initializeBoard() {
    // Reset board - 10 ô dân, mỗi ô có 5 quân
    board = List.filled(10, 5);
    quan1 = 10; // Quan trái
    quan2 = 10; // Quan phải
    currentPlayer = Player.player1;
    status = GameStatus.playing;
    player1Score = 0;
    player2Score = 0;
  }
  
  // <PERSON><PERSON><PERSON> tra nước đi hợp lệ
  bool isValidMove(int position) {
    if (status != GameStatus.playing) return false;

    // Kiểm tra vị trí thuộc về người chơi hiện tại
    if (currentPlayer == Player.player1) {
      // Player 1 chơi ô 0-4 (phía dưới)
      if (position < 0 || position > 4) return false;
    } else {
      // Player 2 chơi ô 5-9 (phía trên)
      if (position < 5 || position > 9) return false;
    }

    // Ô phải có quân
    return board[position] > 0;
  }
  
  // Thực hiện nước đi
  bool makeMove(int position) {
    if (!isValidMove(position)) return false;

    int stones = board[position];
    board[position] = 0;

    int currentPos = position;
    bool isClockwise = true; // Bắt đầu rải theo chiều kim đồng hồ

    // Rải quân
    while (stones > 0) {
      currentPos = _getNextPosition(currentPos, isClockwise);

      // Rải quân vào ô
      if (currentPos == -1) { // Quan trái
        if (quan1 > 0) {
          quan1++;
        } else {
          // Nếu quan trái rỗng, bỏ qua và tiếp tục
          continue;
        }
      } else if (currentPos == -2) { // Quan phải
        if (quan2 > 0) {
          quan2++;
        } else {
          // Nếu quan phải rỗng, bỏ qua và tiếp tục
          continue;
        }
      } else if (currentPos >= 0 && currentPos < 10) {
        board[currentPos]++;
      }

      stones--;
    }

    // Kiểm tra ăn quân liên tiếp
    _performCapture(currentPos, isClockwise);

    // Kiểm tra kết thúc game
    _checkGameEnd();

    // Chuyển lượt
    if (status == GameStatus.playing) {
      currentPlayer = currentPlayer == Player.player1 ? Player.player2 : Player.player1;
    }

    return true;
  }
  
  int _getNextPosition(int currentPos, bool isClockwise) {
    if (isClockwise) {
      // Chiều kim đồng hồ: 0->1->2->3->4->quan2->9->8->7->6->5->quan1->0
      if (currentPos >= 0 && currentPos < 4) {
        return currentPos + 1;
      } else if (currentPos == 4) {
        return -2; // Đến quan phải
      } else if (currentPos == -2) {
        return 9; // Từ quan phải đến ô 9
      } else if (currentPos > 5 && currentPos <= 9) {
        return currentPos - 1;
      } else if (currentPos == 5) {
        return -1; // Đến quan trái
      } else if (currentPos == -1) {
        return 0; // Từ quan trái đến ô 0
      }
    } else {
      // Ngược chiều kim đồng hồ: 0->quan1->5->6->7->8->9->quan2->4->3->2->1->0
      if (currentPos == 0) {
        return -1; // Đến quan trái
      } else if (currentPos == -1) {
        return 5; // Từ quan trái đến ô 5
      } else if (currentPos >= 5 && currentPos < 9) {
        return currentPos + 1;
      } else if (currentPos == 9) {
        return -2; // Đến quan phải
      } else if (currentPos == -2) {
        return 4; // Từ quan phải đến ô 4
      } else if (currentPos > 0 && currentPos <= 4) {
        return currentPos - 1;
      }
    }
    return 0;
  }
  
  void _performCapture(int lastPosition, bool isClockwise) {
    // Ăn quân liên tiếp theo luật Ô ăn quan
    while (true) {
      // Kiểm tra ăn quan
      if (lastPosition == -1 && quan1 > 0) {
        // Ăn quan trái
        if (currentPlayer == Player.player1) {
          player1Score += quan1;
        } else {
          player2Score += quan1;
        }
        quan1 = 0;

        // Tiếp tục ăn ô tiếp theo
        lastPosition = _getNextPosition(lastPosition, isClockwise);
        continue;
      } else if (lastPosition == -2 && quan2 > 0) {
        // Ăn quan phải
        if (currentPlayer == Player.player1) {
          player1Score += quan2;
        } else {
          player2Score += quan2;
        }
        quan2 = 0;

        // Tiếp tục ăn ô tiếp theo
        lastPosition = _getNextPosition(lastPosition, isClockwise);
        continue;
      }

      // Kiểm tra ăn ô dân
      if (lastPosition >= 0 && lastPosition < 10 && board[lastPosition] > 0) {
        if (currentPlayer == Player.player1) {
          player1Score += board[lastPosition];
        } else {
          player2Score += board[lastPosition];
        }
        board[lastPosition] = 0;

        // Tiếp tục ăn ô tiếp theo
        lastPosition = _getNextPosition(lastPosition, isClockwise);
      } else {
        // Không thể ăn thêm, dừng lại
        break;
      }
    }
  }
  
  void _checkGameEnd() {
    // Kiểm tra nếu một bên không còn quân để đi
    bool player1HasMoves = false;
    bool player2HasMoves = false;

    // Player 1 chơi ô 0-4
    for (int i = 0; i < 5; i++) {
      if (board[i] > 0) player1HasMoves = true;
    }

    // Player 2 chơi ô 5-9
    for (int i = 5; i < 10; i++) {
      if (board[i] > 0) player2HasMoves = true;
    }

    // Game kết thúc khi cả 2 quan đều bị ăn hoặc một bên không còn quân
    bool bothQuanEaten = (quan1 == 0 && quan2 == 0);

    if (!player1HasMoves || !player2HasMoves || bothQuanEaten) {
      status = GameStatus.gameOver;

      // Tính điểm cuối game - quân còn lại thuộc về người chơi tương ứng
      for (int i = 0; i < 5; i++) {
        player1Score += board[i];
      }
      for (int i = 5; i < 10; i++) {
        player2Score += board[i];
      }

      // Quan còn lại cũng được tính điểm
      player1Score += quan1;
      player2Score += quan2;
    }
  }
  
  Player? getWinner() {
    if (status != GameStatus.gameOver) return null;
    
    if (player1Score > player2Score) return Player.player1;
    if (player2Score > player1Score) return Player.player2;
    return null; // Hòa
  }
  
  void resetGame() {
    _initializeBoard();
  }
  
  // Lấy danh sách nước đi hợp lệ cho AI
  List<int> getValidMoves() {
    List<int> moves = [];
    int start = currentPlayer == Player.player1 ? 0 : 5;
    int end = currentPlayer == Player.player1 ? 5 : 10;

    for (int i = start; i < end; i++) {
      if (board[i] > 0) {
        moves.add(i);
      }
    }

    return moves;
  }
}
