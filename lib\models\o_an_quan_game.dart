enum Player { player1, player2 }

enum GameStatus { playing, gameOver }

class OAnQuanGame {
  // Bàn cờ có 12 ô (6 ô mỗi bên) + 2 ô quan
  List<int> board = List.filled(12, 5); // 12 ô dân, mỗi ô có 5 quân
  int quan1 = 10; // Quan bên trái
  int quan2 = 10; // Quan bên phải
  
  Player currentPlayer = Player.player1;
  GameStatus status = GameStatus.playing;
  
  int player1Score = 0;
  int player2Score = 0;
  
  // Constructor
  OAnQuanGame() {
    _initializeBoard();
  }
  
  void _initializeBoard() {
    // Reset board
    board = List.filled(12, 5);
    quan1 = 10;
    quan2 = 10;
    currentPlayer = Player.player1;
    status = GameStatus.playing;
    player1Score = 0;
    player2Score = 0;
  }
  
  // Kiểm tra nước đi hợp lệ
  bool isValidMove(int position) {
    if (status != GameStatus.playing) return false;
    
    // Kiể<PERSON> tra vị trí thuộ<PERSON> về người chơi hiện tại
    if (currentPlayer == Player.player1) {
      // Player 1 chơi ô 0-5
      if (position < 0 || position > 5) return false;
    } else {
      // Player 2 chơi ô 6-11
      if (position < 6 || position > 11) return false;
    }
    
    // Ô phải có quân
    return board[position] > 0;
  }
  
  // Thực hiện nước đi
  bool makeMove(int position) {
    if (!isValidMove(position)) return false;
    
    int stones = board[position];
    board[position] = 0;
    
    int currentPos = position;
    
    // Rải quân
    while (stones > 0) {
      currentPos = _getNextPosition(currentPos);
      
      // Nếu là ô quan và có quân thì bỏ qua
      if (currentPos == -1 && quan1 > 0) {
        quan1++;
      } else if (currentPos == -2 && quan2 > 0) {
        quan2++;
      } else if (currentPos >= 0 && currentPos < 12) {
        board[currentPos]++;
      }
      
      stones--;
    }
    
    // Kiểm tra ăn quân
    _checkCapture(currentPos);
    
    // Kiểm tra kết thúc game
    _checkGameEnd();
    
    // Chuyển lượt
    if (status == GameStatus.playing) {
      currentPlayer = currentPlayer == Player.player1 ? Player.player2 : Player.player1;
    }
    
    return true;
  }
  
  int _getNextPosition(int currentPos) {
    // Logic di chuyển theo chiều kim đồng hồ
    if (currentPos == 5) return -2; // Từ ô 5 đến quan phải
    if (currentPos == -2) return 11; // Từ quan phải đến ô 11
    if (currentPos == 6) return -1; // Từ ô 6 đến quan trái
    if (currentPos == -1) return 0; // Từ quan trái đến ô 0
    
    if (currentPos >= 0 && currentPos < 5) return currentPos + 1;
    if (currentPos >= 7 && currentPos < 11) return currentPos + 1;
    if (currentPos == 11) return 10;
    if (currentPos >= 7 && currentPos > 6) return currentPos - 1;
    
    return (currentPos + 1) % 12;
  }
  
  void _checkCapture(int lastPosition) {
    // Logic ăn quân (đơn giản hóa)
    if (lastPosition >= 0 && lastPosition < 12) {
      if (board[lastPosition] == 2 || board[lastPosition] == 3) {
        if (currentPlayer == Player.player1) {
          player1Score += board[lastPosition];
        } else {
          player2Score += board[lastPosition];
        }
        board[lastPosition] = 0;
      }
    }
  }
  
  void _checkGameEnd() {
    // Kiểm tra nếu một bên không còn quân để đi
    bool player1HasMoves = false;
    bool player2HasMoves = false;
    
    for (int i = 0; i < 6; i++) {
      if (board[i] > 0) player1HasMoves = true;
    }
    
    for (int i = 6; i < 12; i++) {
      if (board[i] > 0) player2HasMoves = true;
    }
    
    if (!player1HasMoves || !player2HasMoves) {
      status = GameStatus.gameOver;
      
      // Tính điểm cuối game
      for (int i = 0; i < 6; i++) {
        player1Score += board[i];
      }
      for (int i = 6; i < 12; i++) {
        player2Score += board[i];
      }
      
      player1Score += quan1;
      player2Score += quan2;
    }
  }
  
  Player? getWinner() {
    if (status != GameStatus.gameOver) return null;
    
    if (player1Score > player2Score) return Player.player1;
    if (player2Score > player1Score) return Player.player2;
    return null; // Hòa
  }
  
  void resetGame() {
    _initializeBoard();
  }
  
  // Lấy danh sách nước đi hợp lệ cho AI
  List<int> getValidMoves() {
    List<int> moves = [];
    int start = currentPlayer == Player.player1 ? 0 : 6;
    int end = currentPlayer == Player.player1 ? 6 : 12;
    
    for (int i = start; i < end; i++) {
      if (board[i] > 0) {
        moves.add(i);
      }
    }
    
    return moves;
  }
}
