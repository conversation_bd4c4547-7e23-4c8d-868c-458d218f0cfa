class GameInfo {
  final String id;
  final String name;
  final String description;
  final String imagePath;
  final GameDifficulty difficulty;
  final int maxPlayers;

  const GameInfo({
    required this.id,
    required this.name,
    required this.description,
    required this.imagePath,
    required this.difficulty,
    required this.maxPlayers,
  });
}

enum GameDifficulty {
  easy,
  medium,
  hard,
}

extension GameDifficultyExtension on GameDifficulty {
  String get displayName {
    switch (this) {
      case GameDifficulty.easy:
        return 'Dễ';
      case GameDifficulty.medium:
        return 'Trung bình';
      case GameDifficulty.hard:
        return 'Khó';
    }
  }
}
