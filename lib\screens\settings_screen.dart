import 'package:flutter/material.dart';
import '../services/storage_service.dart';
import '../services/sound_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../widgets/feedback_widget.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late GameSettings settings;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    settings = await StorageService().getGameSettings();
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _saveSettings() async {
    await StorageService().saveGameSettings(settings);
    
    // Update sound service settings
    await SoundService().setSoundEnabled(settings.soundEnabled);
    await SoundService().setMusicEnabled(settings.musicEnabled);
    await SoundService().setSoundVolume(settings.soundVolume);
    await SoundService().setMusicVolume(settings.musicVolume);
    
    if (mounted) {
      FeedbackManager.showSuccess(context, 'Đã lưu cài đặt');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Cài đặt'),
        backgroundColor: AppColors.primaryRed,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.background, AppColors.creamWhite],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          children: [
            _buildSectionHeader('Âm thanh'),
            _buildSoundSettings(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            _buildSectionHeader('Trò chơi'),
            _buildGameSettings(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            _buildSectionHeader('Khác'),
            _buildOtherSettings(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildSoundSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            SwitchListTile(
              title: const Text('Bật âm thanh'),
              subtitle: const Text('Âm thanh hiệu ứng trong game'),
              value: settings.soundEnabled,
              onChanged: (value) {
                setState(() {
                  settings.soundEnabled = value;
                });
              },
            ),
            
            SwitchListTile(
              title: const Text('Bật nhạc nền'),
              subtitle: const Text('Nhạc nền trong game'),
              value: settings.musicEnabled,
              onChanged: (value) {
                setState(() {
                  settings.musicEnabled = value;
                });
              },
            ),
            
            ListTile(
              title: const Text('Âm lượng hiệu ứng'),
              subtitle: Slider(
                value: settings.soundVolume,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: '${(settings.soundVolume * 100).round()}%',
                onChanged: settings.soundEnabled ? (value) {
                  setState(() {
                    settings.soundVolume = value;
                  });
                } : null,
              ),
            ),
            
            ListTile(
              title: const Text('Âm lượng nhạc nền'),
              subtitle: Slider(
                value: settings.musicVolume,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: '${(settings.musicVolume * 100).round()}%',
                onChanged: settings.musicEnabled ? (value) {
                  setState(() {
                    settings.musicVolume = value;
                  });
                } : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            ListTile(
              title: const Text('Độ khó AI'),
              subtitle: Text(_getDifficultyDisplayName(settings.difficulty)),
              trailing: DropdownButton<String>(
                value: settings.difficulty,
                items: const [
                  DropdownMenuItem(value: 'easy', child: Text('Dễ')),
                  DropdownMenuItem(value: 'medium', child: Text('Trung bình')),
                  DropdownMenuItem(value: 'hard', child: Text('Khó')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      settings.difficulty = value;
                    });
                  }
                },
              ),
            ),
            
            SwitchListTile(
              title: const Text('Hiển thị gợi ý'),
              subtitle: const Text('Hiển thị các nước đi có thể'),
              value: settings.showHints,
              onChanged: (value) {
                setState(() {
                  settings.showHints = value;
                });
              },
            ),
            
            SwitchListTile(
              title: const Text('Tự động lưu'),
              subtitle: const Text('Tự động lưu tiến trình game'),
              value: settings.autoSave,
              onChanged: (value) {
                setState(() {
                  settings.autoSave = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOtherSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            ListTile(
              title: const Text('Giao diện'),
              subtitle: Text(_getThemeDisplayName(settings.theme)),
              trailing: DropdownButton<String>(
                value: settings.theme,
                items: const [
                  DropdownMenuItem(value: 'traditional', child: Text('Truyền thống')),
                  DropdownMenuItem(value: 'modern', child: Text('Hiện đại')),
                  DropdownMenuItem(value: 'dark', child: Text('Tối')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      settings.theme = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
            ),
            child: const Text(
              'Lưu cài đặt',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        ),
        
        const SizedBox(height: AppConstants.paddingMedium),
        
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _resetToDefaults,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warning,
              padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
            ),
            child: const Text(
              'Khôi phục mặc định',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        ),
        
        const SizedBox(height: AppConstants.paddingMedium),
        
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _clearAllData,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
            ),
            child: const Text(
              'Xóa tất cả dữ liệu',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        ),
      ],
    );
  }

  String _getDifficultyDisplayName(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'Dễ';
      case 'medium':
        return 'Trung bình';
      case 'hard':
        return 'Khó';
      default:
        return 'Trung bình';
    }
  }

  String _getThemeDisplayName(String theme) {
    switch (theme) {
      case 'traditional':
        return 'Truyền thống';
      case 'modern':
        return 'Hiện đại';
      case 'dark':
        return 'Tối';
      default:
        return 'Truyền thống';
    }
  }

  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Khôi phục mặc định'),
        content: const Text('Bạn có chắc muốn khôi phục tất cả cài đặt về mặc định?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                settings = GameSettings();
              });
              _saveSettings();
            },
            child: const Text('Khôi phục'),
          ),
        ],
      ),
    );
  }

  void _clearAllData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa tất cả dữ liệu'),
        content: const Text('Bạn có chắc muốn xóa tất cả dữ liệu? Hành động này không thể hoàn tác.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await StorageService().clearAllData();
              setState(() {
                settings = GameSettings();
              });
              if (mounted) {
                FeedbackManager.showSuccess(context, 'Đã xóa tất cả dữ liệu');
              }
            },
            child: const Text('Xóa', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
