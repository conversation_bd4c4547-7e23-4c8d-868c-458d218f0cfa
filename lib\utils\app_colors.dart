import 'package:flutter/material.dart';

class AppColors {
  // <PERSON><PERSON><PERSON> sắ<PERSON> truyền thống Việt Nam
  static const Color primaryRed = Color(0xFFDA020E); // Đỏ truyền thống
  static const Color primaryGold = Color(0xFFFFD700); // Vàng kim
  static const Color darkBrown = Color(0xFF8B4513); // Nâu gỗ
  static const Color lightBrown = Color(0xFFDEB887); // Nâu sáng
  static const Color creamWhite = Color(0xFFFFFDD0); // Trắng kem
  static const Color darkGreen = Color(0xFF228B22); // Xanh lá đậm
  
  // M<PERSON>u nền và text
  static const Color background = Color(0xFFF5F5DC); // Beige nhẹ
  static const Color cardBackground = Color(0xFFFFFAF0); // Trắng ngà
  static const Color textPrimary = Color(0xFF2F1B14); // Nâu đậm
  static const Color textSecondary = Color(0xFF8B4513); // <PERSON><PERSON><PERSON> vừa
  
  // Màu trạng thái
  static const Color success = Color(0xFF32CD32);
  static const Color warning = Color(0xFFFF8C00);
  static const Color error = Color(0xFFDC143C);
  
  // Gradient truyền thống
  static const LinearGradient goldGradient = LinearGradient(
    colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient redGradient = LinearGradient(
    colors: [Color(0xFFDA020E), Color(0xFFB22222)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
