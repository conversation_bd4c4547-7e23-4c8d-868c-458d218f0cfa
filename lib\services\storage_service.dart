import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;

  // Initialize storage service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Game Statistics
  Future<void> saveGameStats(String gameId, GameStats stats) async {
    if (_prefs == null) return;
    
    final statsJson = json.encode(stats.toJson());
    await _prefs!.setString('game_stats_$gameId', statsJson);
  }

  Future<GameStats> getGameStats(String gameId) async {
    if (_prefs == null) return GameStats();
    
    final statsJson = _prefs!.getString('game_stats_$gameId');
    if (statsJson == null) return GameStats();
    
    try {
      final statsMap = json.decode(statsJson) as Map<String, dynamic>;
      return GameStats.fromJson(statsMap);
    } catch (e) {
      return GameStats();
    }
  }

  // High Scores
  Future<void> saveHighScore(String gameId, int score) async {
    if (_prefs == null) return;
    
    final currentHighScore = await getHighScore(gameId);
    if (score > currentHighScore) {
      await _prefs!.setInt('high_score_$gameId', score);
    }
  }

  Future<int> getHighScore(String gameId) async {
    if (_prefs == null) return 0;
    return _prefs!.getInt('high_score_$gameId') ?? 0;
  }

  // Game Settings
  Future<void> saveGameSettings(GameSettings settings) async {
    if (_prefs == null) return;
    
    final settingsJson = json.encode(settings.toJson());
    await _prefs!.setString('game_settings', settingsJson);
  }

  Future<GameSettings> getGameSettings() async {
    if (_prefs == null) return GameSettings();
    
    final settingsJson = _prefs!.getString('game_settings');
    if (settingsJson == null) return GameSettings();
    
    try {
      final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
      return GameSettings.fromJson(settingsMap);
    } catch (e) {
      return GameSettings();
    }
  }

  // Game Progress
  Future<void> saveGameProgress(String gameId, Map<String, dynamic> progress) async {
    if (_prefs == null) return;
    
    final progressJson = json.encode(progress);
    await _prefs!.setString('game_progress_$gameId', progressJson);
  }

  Future<Map<String, dynamic>?> getGameProgress(String gameId) async {
    if (_prefs == null) return null;
    
    final progressJson = _prefs!.getString('game_progress_$gameId');
    if (progressJson == null) return null;
    
    try {
      return json.decode(progressJson) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  Future<void> clearGameProgress(String gameId) async {
    if (_prefs == null) return;
    await _prefs!.remove('game_progress_$gameId');
  }

  // User Preferences
  Future<void> setFirstLaunch(bool isFirstLaunch) async {
    if (_prefs == null) return;
    await _prefs!.setBool('first_launch', isFirstLaunch);
  }

  Future<bool> isFirstLaunch() async {
    if (_prefs == null) return true;
    return _prefs!.getBool('first_launch') ?? true;
  }

  Future<void> setLastPlayedGame(String gameId) async {
    if (_prefs == null) return;
    await _prefs!.setString('last_played_game', gameId);
  }

  Future<String?> getLastPlayedGame() async {
    if (_prefs == null) return null;
    return _prefs!.getString('last_played_game');
  }

  // Clear all data
  Future<void> clearAllData() async {
    if (_prefs == null) return;
    await _prefs!.clear();
  }

  // Export/Import data
  Future<Map<String, dynamic>> exportData() async {
    if (_prefs == null) return {};
    
    final keys = _prefs!.getKeys();
    final data = <String, dynamic>{};
    
    for (String key in keys) {
      final value = _prefs!.get(key);
      data[key] = value;
    }
    
    return data;
  }

  Future<void> importData(Map<String, dynamic> data) async {
    if (_prefs == null) return;
    
    for (String key in data.keys) {
      final value = data[key];
      
      if (value is String) {
        await _prefs!.setString(key, value);
      } else if (value is int) {
        await _prefs!.setInt(key, value);
      } else if (value is double) {
        await _prefs!.setDouble(key, value);
      } else if (value is bool) {
        await _prefs!.setBool(key, value);
      } else if (value is List<String>) {
        await _prefs!.setStringList(key, value);
      }
    }
  }
}

// Game Statistics Model
class GameStats {
  int gamesPlayed;
  int gamesWon;
  int gamesLost;
  int totalScore;
  int bestScore;
  Duration totalPlayTime;
  DateTime? lastPlayed;

  GameStats({
    this.gamesPlayed = 0,
    this.gamesWon = 0,
    this.gamesLost = 0,
    this.totalScore = 0,
    this.bestScore = 0,
    this.totalPlayTime = Duration.zero,
    this.lastPlayed,
  });

  double get winRate => gamesPlayed > 0 ? gamesWon / gamesPlayed : 0.0;
  double get averageScore => gamesPlayed > 0 ? totalScore / gamesPlayed : 0.0;

  // Update game statistics
  void updateStats({
    bool? won,
    int? score,
    Duration? playTime,
  }) {
    gamesPlayed++;
    if (won == true) gamesWon++;
    if (won == false) gamesLost++;
    if (score != null) {
      totalScore += score;
      if (score > bestScore) bestScore = score;
    }
    if (playTime != null) {
      totalPlayTime += playTime;
    }
    lastPlayed = DateTime.now();
  }

  Map<String, dynamic> toJson() {
    return {
      'gamesPlayed': gamesPlayed,
      'gamesWon': gamesWon,
      'gamesLost': gamesLost,
      'totalScore': totalScore,
      'bestScore': bestScore,
      'totalPlayTime': totalPlayTime.inMilliseconds,
      'lastPlayed': lastPlayed?.millisecondsSinceEpoch,
    };
  }

  factory GameStats.fromJson(Map<String, dynamic> json) {
    return GameStats(
      gamesPlayed: json['gamesPlayed'] ?? 0,
      gamesWon: json['gamesWon'] ?? 0,
      gamesLost: json['gamesLost'] ?? 0,
      totalScore: json['totalScore'] ?? 0,
      bestScore: json['bestScore'] ?? 0,
      totalPlayTime: Duration(milliseconds: json['totalPlayTime'] ?? 0),
      lastPlayed: json['lastPlayed'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['lastPlayed'])
          : null,
    );
  }
}

// Game Settings Model
class GameSettings {
  bool soundEnabled;
  bool musicEnabled;
  double soundVolume;
  double musicVolume;
  String difficulty;
  bool showHints;
  bool autoSave;
  String theme;

  GameSettings({
    this.soundEnabled = true,
    this.musicEnabled = true,
    this.soundVolume = 0.7,
    this.musicVolume = 0.5,
    this.difficulty = 'medium',
    this.showHints = true,
    this.autoSave = true,
    this.theme = 'traditional',
  });

  Map<String, dynamic> toJson() {
    return {
      'soundEnabled': soundEnabled,
      'musicEnabled': musicEnabled,
      'soundVolume': soundVolume,
      'musicVolume': musicVolume,
      'difficulty': difficulty,
      'showHints': showHints,
      'autoSave': autoSave,
      'theme': theme,
    };
  }

  factory GameSettings.fromJson(Map<String, dynamic> json) {
    return GameSettings(
      soundEnabled: json['soundEnabled'] ?? true,
      musicEnabled: json['musicEnabled'] ?? true,
      soundVolume: json['soundVolume'] ?? 0.7,
      musicVolume: json['musicVolume'] ?? 0.5,
      difficulty: json['difficulty'] ?? 'medium',
      showHints: json['showHints'] ?? true,
      autoSave: json['autoSave'] ?? true,
      theme: json['theme'] ?? 'traditional',
    );
  }
}
