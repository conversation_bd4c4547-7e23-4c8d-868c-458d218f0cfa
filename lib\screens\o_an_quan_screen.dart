import 'package:flutter/material.dart';
import '../models/o_an_quan_game.dart';
import '../services/o_an_quan_ai.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';

class OAnQuanScreen extends StatefulWidget {
  const OAnQuanScreen({super.key});

  @override
  State<OAnQuanScreen> createState() => _OAnQuanScreenState();
}

class _OAnQuanScreenState extends State<OAnQuanScreen> {
  late OAnQuanGame game;
  late OAnQuanAI ai;
  bool isAIThinking = false;

  @override
  void initState() {
    super.initState();
    game = OAnQuanGame();
    ai = OAnQuanAI(difficulty: AIDifficulty.medium);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ô Ăn Quan'),
        backgroundColor: AppColors.primaryRed,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetGame,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.background, AppColors.creamWhite],
          ),
        ),
        child: Column(
          children: [
            _buildScoreBoard(),
            Expanded(
              child: _buildGameBoard(),
            ),
            _buildGameControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreBoard() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: AppColors.primaryGold.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildPlayerScore('Bạn', game.player1Score, game.currentPlayer == Player.player1),
          _buildPlayerScore('Máy', game.player2Score, game.currentPlayer == Player.player2),
        ],
      ),
    );
  }

  Widget _buildPlayerScore(String name, int score, bool isCurrentPlayer) {
    return Column(
      children: [
        Text(
          name,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isCurrentPlayer ? AppColors.primaryRed : AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isCurrentPlayer ? AppColors.primaryRed : AppColors.lightBrown,
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          ),
          child: Text(
            score.toString(),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isCurrentPlayer ? Colors.white : AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGameBoard() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.darkBrown,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // AI's side (top row - reversed)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              for (int i = 11; i >= 6; i--)
                _buildCell(i, false),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Quan positions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildQuanCell(game.quan1, 'Quan 1'),
              _buildQuanCell(game.quan2, 'Quan 2'),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Player's side (bottom row)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              for (int i = 0; i < 6; i++)
                _buildCell(i, true),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCell(int index, bool isPlayerSide) {
    bool canPlay = game.isValidMove(index) && !isAIThinking;
    int stones = game.board[index];
    
    return GestureDetector(
      onTap: canPlay ? () => _makeMove(index) : null,
      child: Container(
        width: 45,
        height: 45,
        decoration: BoxDecoration(
          color: canPlay ? AppColors.primaryGold : AppColors.lightBrown,
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          border: Border.all(
            color: canPlay ? AppColors.primaryRed : AppColors.darkBrown,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            stones.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: canPlay ? AppColors.darkBrown : AppColors.textSecondary,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuanCell(int stones, String label) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: AppColors.primaryGold,
        shape: BoxShape.circle,
        border: Border.all(color: AppColors.primaryRed, width: 3),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            stones.toString(),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.darkBrown,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 8,
              color: AppColors.darkBrown,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameControls() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          if (game.status == GameStatus.gameOver)
            _buildGameOverMessage(),
          
          if (isAIThinking)
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('AI đang suy nghĩ...'),
              ],
            ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _resetGame,
                child: const Text('Chơi lại'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Thoát'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGameOverMessage() {
    Player? winner = game.getWinner();
    String message;
    Color color;
    
    if (winner == Player.player1) {
      message = 'Chúc mừng! Bạn đã thắng!';
      color = AppColors.success;
    } else if (winner == Player.player2) {
      message = 'AI đã thắng! Thử lại nhé!';
      color = AppColors.error;
    } else {
      message = 'Hòa! Trận đấu cân bằng!';
      color = AppColors.warning;
    }
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: color),
      ),
      child: Text(
        message,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  void _makeMove(int position) {
    if (game.makeMove(position)) {
      setState(() {});
      
      // AI turn
      if (game.currentPlayer == Player.player2 && game.status == GameStatus.playing) {
        _makeAIMove();
      }
    }
  }

  void _makeAIMove() async {
    setState(() {
      isAIThinking = true;
    });
    
    // Delay để tạo cảm giác AI đang suy nghĩ
    await Future.delayed(const Duration(milliseconds: 1000));
    
    int aiMove = ai.getBestMove(game);
    if (aiMove != -1) {
      game.makeMove(aiMove);
    }
    
    setState(() {
      isAIThinking = false;
    });
  }

  void _resetGame() {
    setState(() {
      game.resetGame();
      isAIThinking = false;
    });
  }
}
