import 'package:flutter/material.dart';
import '../models/game_info.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';

class GameCard extends StatelessWidget {
  final GameInfo gameInfo;
  final VoidCallback onTap;

  const GameCard({
    super.key,
    required this.gameInfo,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      shadowColor: AppColors.darkBrown.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        side: BorderSide(
          color: AppColors.primaryGold.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.cardBackground,
                AppColors.cardBackground.withOpacity(0.8),
              ],
            ),
          ),
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Game icon placeholder
              Container(
                height: 80,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                  gradient: AppColors.goldGradient,
                ),
                child: Icon(
                  _getGameIcon(),
                  size: 40,
                  color: AppColors.darkBrown,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Game name
              Text(
                gameInfo.name,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              
              // Game description
              Text(
                gameInfo.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Game info row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildInfoChip(
                    icon: Icons.psychology,
                    label: gameInfo.difficulty.displayName,
                    color: _getDifficultyColor(),
                  ),
                  _buildInfoChip(
                    icon: Icons.people,
                    label: '${gameInfo.maxPlayers} người',
                    color: AppColors.darkGreen,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getGameIcon() {
    switch (gameInfo.id) {
      case 'o_an_quan':
        return Icons.circle_outlined;
      case 'co_ca_ngua':
        return Icons.casino;
      case 'co_ganh':
        return Icons.grid_on;
      default:
        return Icons.games;
    }
  }

  Color _getDifficultyColor() {
    switch (gameInfo.difficulty) {
      case GameDifficulty.easy:
        return AppColors.success;
      case GameDifficulty.medium:
        return AppColors.warning;
      case GameDifficulty.hard:
        return AppColors.error;
    }
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
