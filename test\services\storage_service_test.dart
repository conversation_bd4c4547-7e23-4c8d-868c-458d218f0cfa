import 'package:flutter_test/flutter_test.dart';
import 'package:traditional_game/services/storage_service.dart';

void main() {
  group('StorageService Tests', () {
    test('GameStats initializes correctly', () {
      GameStats stats = GameStats();
      
      expect(stats.gamesPlayed, 0);
      expect(stats.gamesWon, 0);
      expect(stats.gamesLost, 0);
      expect(stats.totalScore, 0);
      expect(stats.bestScore, 0);
      expect(stats.totalPlayTime, Duration.zero);
      expect(stats.lastPlayed, null);
      expect(stats.winRate, 0.0);
      expect(stats.averageScore, 0.0);
    });

    test('GameStats calculations work correctly', () {
      GameStats stats = GameStats(
        gamesPlayed: 10,
        gamesWon: 7,
        totalScore: 500,
      );
      
      expect(stats.winRate, 0.7);
      expect(stats.averageScore, 50.0);
    });

    test('GameStats updateStats works correctly', () {
      GameStats stats = GameStats();
      
      // Update with a win
      stats.updateStats(won: true, score: 100, playTime: const Duration(minutes: 5));
      
      expect(stats.gamesPlayed, 1);
      expect(stats.gamesWon, 1);
      expect(stats.gamesLost, 0);
      expect(stats.totalScore, 100);
      expect(stats.bestScore, 100);
      expect(stats.totalPlayTime, const Duration(minutes: 5));
      expect(stats.lastPlayed, isNotNull);
      
      // Update with a loss
      stats.updateStats(won: false, score: 50, playTime: const Duration(minutes: 3));
      
      expect(stats.gamesPlayed, 2);
      expect(stats.gamesWon, 1);
      expect(stats.gamesLost, 1);
      expect(stats.totalScore, 150);
      expect(stats.bestScore, 100); // Should remain 100
      expect(stats.totalPlayTime, const Duration(minutes: 8));
      
      // Update with higher score
      stats.updateStats(won: true, score: 150);
      
      expect(stats.bestScore, 150); // Should update to 150
    });

    test('GameStats JSON serialization works', () {
      GameStats original = GameStats(
        gamesPlayed: 5,
        gamesWon: 3,
        gamesLost: 2,
        totalScore: 250,
        bestScore: 80,
        totalPlayTime: const Duration(hours: 1, minutes: 30),
      );
      
      // Convert to JSON and back
      Map<String, dynamic> json = original.toJson();
      GameStats restored = GameStats.fromJson(json);
      
      expect(restored.gamesPlayed, original.gamesPlayed);
      expect(restored.gamesWon, original.gamesWon);
      expect(restored.gamesLost, original.gamesLost);
      expect(restored.totalScore, original.totalScore);
      expect(restored.bestScore, original.bestScore);
      expect(restored.totalPlayTime, original.totalPlayTime);
    });

    test('GameSettings initializes correctly', () {
      GameSettings settings = GameSettings();
      
      expect(settings.soundEnabled, true);
      expect(settings.musicEnabled, true);
      expect(settings.soundVolume, 0.7);
      expect(settings.musicVolume, 0.5);
      expect(settings.difficulty, 'medium');
      expect(settings.showHints, true);
      expect(settings.autoSave, true);
      expect(settings.theme, 'traditional');
    });

    test('GameSettings JSON serialization works', () {
      GameSettings original = GameSettings(
        soundEnabled: false,
        musicEnabled: true,
        soundVolume: 0.8,
        musicVolume: 0.3,
        difficulty: 'hard',
        showHints: false,
        autoSave: false,
        theme: 'dark',
      );
      
      // Convert to JSON and back
      Map<String, dynamic> json = original.toJson();
      GameSettings restored = GameSettings.fromJson(json);
      
      expect(restored.soundEnabled, original.soundEnabled);
      expect(restored.musicEnabled, original.musicEnabled);
      expect(restored.soundVolume, original.soundVolume);
      expect(restored.musicVolume, original.musicVolume);
      expect(restored.difficulty, original.difficulty);
      expect(restored.showHints, original.showHints);
      expect(restored.autoSave, original.autoSave);
      expect(restored.theme, original.theme);
    });
  });
}
