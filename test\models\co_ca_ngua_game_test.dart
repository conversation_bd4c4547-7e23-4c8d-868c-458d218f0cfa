import 'package:flutter_test/flutter_test.dart';
import 'package:traditional_game/models/co_ca_ngua_game.dart';

void main() {
  group('CoCaNguaGame Tests', () {
    late CoCaNguaGame game;

    setUp(() {
      game = CoCaNguaGame();
    });

    test('Game initializes correctly', () {
      expect(game.currentPlayer, CoCaNguaPlayer.player);
      expect(game.isGameActive, true);
      expect(game.playerScore, 100);
      expect(game.aiScore, 100);
      expect(game.playerBets.isEmpty, true);
      expect(game.aiBets.isEmpty, true);
      expect(game.diceResults.isEmpty, true);
    });

    test('Betting works correctly', () {
      // Place a valid bet
      bool result = game.placeBet(GamePiece.fish, 10);
      expect(result, true);
      expect(game.playerBets[GamePiece.fish], 10);
      expect(game.getTotalPlayerBets(), 10);
    });

    test('Invalid bets are rejected', () {
      // Try to bet more than available score
      bool result = game.placeBet(GamePiece.fish, 150);
      expect(result, false);
      expect(game.playerBets.isEmpty, true);
      
      // Try to bet negative amount
      result = game.placeBet(GamePiece.fish, -10);
      expect(result, false);
      expect(game.playerBets.isEmpty, true);
    });

    test('Multiple bets work correctly', () {
      game.placeBet(GamePiece.fish, 10);
      game.placeBet(GamePiece.crab, 20);
      game.placeBet(GamePiece.fish, 5); // Add to existing bet
      
      expect(game.playerBets[GamePiece.fish], 15);
      expect(game.playerBets[GamePiece.crab], 20);
      expect(game.getTotalPlayerBets(), 35);
    });

    test('Bet clearing works', () {
      game.placeBet(GamePiece.fish, 10);
      game.placeBet(GamePiece.crab, 20);
      
      game.clearBet(GamePiece.fish);
      expect(game.playerBets.containsKey(GamePiece.fish), false);
      expect(game.playerBets[GamePiece.crab], 20);
      
      game.clearAllBets();
      expect(game.playerBets.isEmpty, true);
    });

    test('Can place bet check works', () {
      expect(game.canPlaceBet(50), true);
      expect(game.canPlaceBet(150), false);
      
      game.placeBet(GamePiece.fish, 80);
      expect(game.canPlaceBet(30), false); // Would exceed total score
      expect(game.canPlaceBet(20), true);
    });

    test('Game piece extensions work', () {
      expect(GamePiece.fish.displayName, 'Cá');
      expect(GamePiece.crab.displayName, 'Cua');
      expect(GamePiece.shrimp.displayName, 'Tôm');
      expect(GamePiece.rooster.displayName, 'Gà');
      expect(GamePiece.gourd.displayName, 'Bầu');
      expect(GamePiece.deer.displayName, 'Nai');
      
      expect(GamePiece.fish.emoji, '🐟');
      expect(GamePiece.crab.emoji, '🦀');
      expect(GamePiece.shrimp.emoji, '🦐');
      expect(GamePiece.rooster.emoji, '🐓');
      expect(GamePiece.gourd.emoji, '🎃');
      expect(GamePiece.deer.emoji, '🦌');
    });

    test('Game reset works', () {
      // Make some changes
      game.placeBet(GamePiece.fish, 10);
      game.playerScore = 50;
      game.aiScore = 80;
      game.isGameActive = false;
      
      // Reset game
      game.resetGame();
      
      // Check if reset worked
      expect(game.playerScore, 100);
      expect(game.aiScore, 100);
      expect(game.isGameActive, true);
      expect(game.playerBets.isEmpty, true);
      expect(game.aiBets.isEmpty, true);
      expect(game.diceResults.isEmpty, true);
    });
  });
}
