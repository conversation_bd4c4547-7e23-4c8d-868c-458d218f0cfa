enum CoGanhPlayer { player1, player2 }

enum PieceType { empty, player1, player2 }

class Position {
  final int row;
  final int col;
  
  Position(this.row, this.col);
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Position && other.row == row && other.col == col;
  }
  
  @override
  int get hashCode => row.hashCode ^ col.hashCode;
  
  @override
  String toString() => '($row, $col)';
}

class CoGanhGame {
  // Bàn cờ 5x5
  static const int boardSize = 5;
  List<List<PieceType>> board = [];
  
  CoGanhPlayer currentPlayer = CoGanhPlayer.player1;
  bool isGameActive = true;
  
  int player1Pieces = 12;
  int player2Pieces = 12;
  
  // Lịch sử nước đi
  List<Move> moveHistory = [];
  
  CoGanhGame() {
    _initializeBoard();
  }
  
  void _initializeBoard() {
    board = List.generate(
      boardSize,
      (row) => List.generate(boardSize, (col) => PieceType.empty),
    );
    
    // Đặt quân ban đầu
    // Player 1 (bottom)
    for (int col = 0; col < boardSize; col++) {
      board[0][col] = PieceType.player1;
      board[1][col] = PieceType.player1;
    }
    board[2][0] = PieceType.player1;
    board[2][1] = PieceType.player1;
    
    // Player 2 (top)
    for (int col = 0; col < boardSize; col++) {
      board[4][col] = PieceType.player2;
      board[3][col] = PieceType.player2;
    }
    board[2][3] = PieceType.player2;
    board[2][4] = PieceType.player2;
    
    currentPlayer = CoGanhPlayer.player1;
    isGameActive = true;
    player1Pieces = 12;
    player2Pieces = 12;
    moveHistory.clear();
  }
  
  // Kiểm tra nước đi hợp lệ
  bool isValidMove(Position from, Position to) {
    if (!isGameActive) return false;
    
    // Kiểm tra vị trí trong bàn cờ
    if (!_isInBoard(from) || !_isInBoard(to)) return false;
    
    // Kiểm tra ô đích trống
    if (board[to.row][to.col] != PieceType.empty) return false;
    
    // Kiểm tra quân thuộc về người chơi hiện tại
    PieceType expectedPiece = currentPlayer == CoGanhPlayer.player1 
        ? PieceType.player1 
        : PieceType.player2;
    
    if (board[from.row][from.col] != expectedPiece) return false;
    
    // Kiểm tra di chuyển hợp lệ (chỉ di chuyển 1 ô theo 8 hướng)
    int rowDiff = (to.row - from.row).abs();
    int colDiff = (to.col - from.col).abs();
    
    return (rowDiff <= 1 && colDiff <= 1) && (rowDiff + colDiff > 0);
  }
  
  // Thực hiện nước đi
  bool makeMove(Position from, Position to) {
    if (!isValidMove(from, to)) return false;
    
    PieceType movingPiece = board[from.row][from.col];
    
    // Di chuyển quân
    board[from.row][from.col] = PieceType.empty;
    board[to.row][to.col] = movingPiece;
    
    // Kiểm tra gánh
    List<Position> capturedPieces = _checkCapture(to);
    
    // Thực hiện gánh
    for (Position pos in capturedPieces) {
      board[pos.row][pos.col] = movingPiece;
      if (currentPlayer == CoGanhPlayer.player1) {
        player2Pieces--;
      } else {
        player1Pieces--;
      }
    }
    
    // Lưu lịch sử
    moveHistory.add(Move(from, to, capturedPieces));
    
    // Kiểm tra kết thúc game
    _checkGameEnd();
    
    // Chuyển lượt
    if (isGameActive) {
      currentPlayer = currentPlayer == CoGanhPlayer.player1 
          ? CoGanhPlayer.player2 
          : CoGanhPlayer.player1;
    }
    
    return true;
  }
  
  // Kiểm tra gánh
  List<Position> _checkCapture(Position pos) {
    List<Position> captured = [];
    PieceType myPiece = board[pos.row][pos.col];
    PieceType enemyPiece = myPiece == PieceType.player1 
        ? PieceType.player2 
        : PieceType.player1;
    
    // Kiểm tra 4 hướng chính và 4 hướng chéo
    List<List<int>> directions = [
      [-1, 0], [1, 0], [0, -1], [0, 1], // 4 hướng chính
      [-1, -1], [-1, 1], [1, -1], [1, 1], // 4 hướng chéo
    ];
    
    for (List<int> dir in directions) {
      Position adjacent1 = Position(pos.row + dir[0], pos.col + dir[1]);
      Position adjacent2 = Position(pos.row - dir[0], pos.col - dir[1]);
      
      // Kiểm tra gánh: quân địch ở giữa 2 quân ta
      if (_isInBoard(adjacent1) && _isInBoard(adjacent2)) {
        if (board[adjacent1.row][adjacent1.col] == enemyPiece &&
            board[adjacent2.row][adjacent2.col] == myPiece) {
          captured.add(adjacent1);
        }
      }
    }
    
    return captured;
  }
  
  bool _isInBoard(Position pos) {
    return pos.row >= 0 && pos.row < boardSize && 
           pos.col >= 0 && pos.col < boardSize;
  }
  
  void _checkGameEnd() {
    // Game kết thúc khi một bên không còn quân hoặc không thể di chuyển
    if (player1Pieces <= 2 || player2Pieces <= 2) {
      isGameActive = false;
      return;
    }
    
    // Kiểm tra có nước đi hợp lệ không
    if (!_hasValidMoves(currentPlayer)) {
      isGameActive = false;
    }
  }
  
  bool _hasValidMoves(CoGanhPlayer player) {
    PieceType pieceType = player == CoGanhPlayer.player1 
        ? PieceType.player1 
        : PieceType.player2;
    
    for (int row = 0; row < boardSize; row++) {
      for (int col = 0; col < boardSize; col++) {
        if (board[row][col] == pieceType) {
          Position from = Position(row, col);
          
          // Kiểm tra tất cả các nước đi có thể
          for (int dr = -1; dr <= 1; dr++) {
            for (int dc = -1; dc <= 1; dc++) {
              if (dr == 0 && dc == 0) continue;
              
              Position to = Position(row + dr, col + dc);
              if (isValidMove(from, to)) {
                return true;
              }
            }
          }
        }
      }
    }
    
    return false;
  }
  
  // Lấy người thắng
  CoGanhPlayer? getWinner() {
    if (isGameActive) return null;
    
    if (player1Pieces > player2Pieces) return CoGanhPlayer.player1;
    if (player2Pieces > player1Pieces) return CoGanhPlayer.player2;
    return null; // Hòa
  }
  
  // Reset game
  void resetGame() {
    _initializeBoard();
  }
  
  // Lấy tất cả nước đi hợp lệ
  List<Move> getValidMoves() {
    List<Move> moves = [];
    PieceType pieceType = currentPlayer == CoGanhPlayer.player1 
        ? PieceType.player1 
        : PieceType.player2;
    
    for (int row = 0; row < boardSize; row++) {
      for (int col = 0; col < boardSize; col++) {
        if (board[row][col] == pieceType) {
          Position from = Position(row, col);
          
          for (int dr = -1; dr <= 1; dr++) {
            for (int dc = -1; dc <= 1; dc++) {
              if (dr == 0 && dc == 0) continue;
              
              Position to = Position(row + dr, col + dc);
              if (isValidMove(from, to)) {
                moves.add(Move(from, to, []));
              }
            }
          }
        }
      }
    }
    
    return moves;
  }
  
  // Tạo bản sao game
  CoGanhGame copy() {
    CoGanhGame newGame = CoGanhGame();
    
    // Copy board
    for (int row = 0; row < boardSize; row++) {
      for (int col = 0; col < boardSize; col++) {
        newGame.board[row][col] = board[row][col];
      }
    }
    
    newGame.currentPlayer = currentPlayer;
    newGame.isGameActive = isGameActive;
    newGame.player1Pieces = player1Pieces;
    newGame.player2Pieces = player2Pieces;
    
    return newGame;
  }
}

class Move {
  final Position from;
  final Position to;
  final List<Position> capturedPieces;
  
  Move(this.from, this.to, this.capturedPieces);
  
  @override
  String toString() => '$from -> $to (captured: ${capturedPieces.length})';
}
