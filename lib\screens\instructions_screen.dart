import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';

class InstructionsScreen extends StatelessWidget {
  const InstructionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hướng dẫn'),
        backgroundColor: AppColors.primaryRed,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.background, AppColors.creamWhite],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          children: [
            _buildGameInstructions('Ô Ăn Quan', _oAnQuanInstructions()),
            const SizedBox(height: AppConstants.paddingLarge),
            
            _buildGameInstructions('<PERSON><PERSON>', _coCaNguaInstructions()),
            const SizedBox(height: AppConstants.paddingLarge),
            
            _buildGameInstructions('C<PERSON>h', _coGanhInstructions()),
            const SizedBox(height: AppConstants.paddingLarge),
            
            _buildGeneralTips(),
          ],
        ),
      ),
    );
  }

  Widget _buildGameInstructions(String title, List<String> instructions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryRed,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            ...instructions.asMap().entries.map((entry) {
              int index = entry.key;
              String instruction = entry.value;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: AppColors.primaryGold,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: AppColors.darkBrown,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Expanded(
                      child: Text(
                        instruction,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralTips() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Mẹo chơi game',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryRed,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            _buildTip(Icons.volume_up, 'Bật âm thanh để có trải nghiệm tốt nhất'),
            _buildTip(Icons.settings, 'Điều chỉnh độ khó AI trong cài đặt'),
            _buildTip(Icons.lightbulb, 'Bật gợi ý nếu bạn là người mới chơi'),
            _buildTip(Icons.save, 'Game tự động lưu tiến trình của bạn'),
            _buildTip(Icons.bar_chart, 'Xem thống kê để theo dõi tiến bộ'),
          ],
        ),
      ),
    );
  }

  Widget _buildTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primaryGold,
            size: 20,
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> _oAnQuanInstructions() {
    return [
      'Mục tiêu: Thu thập nhiều điểm nhất bằng cách ăn quân và quan.',
      'Chọn một ô có quân để bắt đầu lượt đi.',
      'Rải quân theo chiều kim đồng hồ vào các ô tiếp theo.',
      'Nếu ô cuối cùng có 2 hoặc 3 quân, bạn sẽ ăn được những quân đó.',
      'Tiếp tục ăn quân ở ô trước đó nếu cũng có 2 hoặc 3 quân.',
      'Game kết thúc khi một bên không còn quân để đi.',
      'Người có nhiều điểm nhất sẽ thắng.',
    ];
  }

  List<String> _coCaNguaInstructions() {
    return [
      'Mục tiêu: Dự đoán kết quả xúc xắc để thắng điểm.',
      'Bạn bắt đầu với 100 điểm, AI cũng có 100 điểm.',
      'Đặt cược vào các con vật bằng cách nhấn nút + hoặc -.',
      'Mỗi lần đặt cược tối thiểu 10 điểm.',
      'Nhấn "Lắc xúc xắc" để bắt đầu vòng chơi.',
      'Nếu con vật bạn chọn xuất hiện, bạn thắng gấp số lần xuất hiện.',
      'Game kết thúc khi bạn hoặc AI hết điểm.',
    ];
  }

  List<String> _coGanhInstructions() {
    return [
      'Mục tiêu: Gánh hết quân của đối thủ hoặc khiến đối thủ không thể di chuyển.',
      'Mỗi bên bắt đầu với 12 quân cờ trên bàn cờ 5x5.',
      'Chọn quân của bạn (màu xanh) để di chuyển.',
      'Quân chỉ có thể di chuyển 1 ô theo 8 hướng.',
      'Gánh quân địch bằng cách kẹp quân địch giữa 2 quân của bạn.',
      'Quân bị gánh sẽ chuyển thành quân của bạn.',
      'Thắng khi đối thủ còn ít hơn 3 quân hoặc không thể di chuyển.',
    ];
  }
}
