import 'dart:math';

enum CoCaNguaPlayer { player, ai }

enum GamePiece { fish, crab, shrimp, rooster, gourd, deer }

class CoCaNguaGame {
  final Random _random = Random();
  
  // Trạng thái game
  CoCaNguaPlayer currentPlayer = CoCaNguaPlayer.player;
  bool isGameActive = true;
  
  // Điểm số
  int playerScore = 100; // Bắt đầu với 100 điểm
  int aiScore = 100;
  
  // Kết quả xúc xắc (3 xúc xắc)
  List<GamePiece> diceResults = [];
  
  // Lựa chọn cược của người chơi
  Map<GamePiece, int> playerBets = {};
  Map<GamePiece, int> aiBets = {};
  
  // Lịch sử game
  List<GameRound> gameHistory = [];
  
  CoCaNguaGame() {
    _initializeGame();
  }
  
  void _initializeGame() {
    currentPlayer = CoCaNguaPlayer.player;
    isGameActive = true;
    playerBets.clear();
    aiBets.clear();
    diceResults.clear();
  }
  
  // Đặt cược
  bool placeBet(GamePiece piece, int amount) {
    if (!isGameActive || currentPlayer != CoCaNguaPlayer.player) return false;
    if (amount <= 0 || amount > playerScore) return false;
    
    playerBets[piece] = (playerBets[piece] ?? 0) + amount;
    
    // Kiểm tra tổng cược không vượt quá điểm
    int totalBets = playerBets.values.fold(0, (sum, bet) => sum + bet);
    if (totalBets > playerScore) {
      playerBets[piece] = (playerBets[piece] ?? 0) - amount;
      return false;
    }
    
    return true;
  }
  
  // Xóa cược
  void clearBet(GamePiece piece) {
    playerBets.remove(piece);
  }
  
  void clearAllBets() {
    playerBets.clear();
  }
  
  // AI đặt cược ngẫu nhiên
  void _aiPlaceBets() {
    aiBets.clear();
    
    // AI đặt cược ngẫu nhiên từ 10-30 điểm trên 1-3 con vật
    int numBets = _random.nextInt(3) + 1;
    List<GamePiece> availablePieces = GamePiece.values.toList();
    availablePieces.shuffle(_random);
    
    int totalBets = 0;
    for (int i = 0; i < numBets && totalBets < aiScore; i++) {
      GamePiece piece = availablePieces[i];
      int maxBet = min(30, aiScore - totalBets);
      int bet = _random.nextInt(maxBet - 10 + 1) + 10;
      
      aiBets[piece] = bet;
      totalBets += bet;
    }
  }
  
  // Lắc xúc xắc
  void rollDice() {
    if (!isGameActive) return;
    
    // AI đặt cược trước khi lắc
    _aiPlaceBets();
    
    // Lắc 3 xúc xắc
    diceResults.clear();
    for (int i = 0; i < 3; i++) {
      diceResults.add(GamePiece.values[_random.nextInt(GamePiece.values.length)]);
    }
    
    // Tính toán kết quả
    _calculateResults();
    
    // Lưu lịch sử
    gameHistory.add(GameRound(
      diceResults: List.from(diceResults),
      playerBets: Map.from(playerBets),
      aiBets: Map.from(aiBets),
      playerScoreChange: _calculatePlayerScoreChange(),
      aiScoreChange: _calculateAIScoreChange(),
    ));
    
    // Reset cược cho round tiếp theo
    playerBets.clear();
    aiBets.clear();
    
    // Kiểm tra kết thúc game
    _checkGameEnd();
  }
  
  void _calculateResults() {
    // Tính điểm cho người chơi
    int playerWin = 0;
    int playerLose = 0;
    
    for (var entry in playerBets.entries) {
      GamePiece piece = entry.key;
      int bet = entry.value;
      
      int count = diceResults.where((dice) => dice == piece).length;
      if (count > 0) {
        playerWin += bet * count; // Thắng gấp số lần xuất hiện
      } else {
        playerLose += bet; // Thua mất tiền cược
      }
    }
    
    playerScore = playerScore - playerLose + playerWin;
    
    // Tính điểm cho AI
    int aiWin = 0;
    int aiLose = 0;
    
    for (var entry in aiBets.entries) {
      GamePiece piece = entry.key;
      int bet = entry.value;
      
      int count = diceResults.where((dice) => dice == piece).length;
      if (count > 0) {
        aiWin += bet * count;
      } else {
        aiLose += bet;
      }
    }
    
    aiScore = aiScore - aiLose + aiWin;
  }
  
  int _calculatePlayerScoreChange() {
    int win = 0;
    int lose = 0;
    
    for (var entry in playerBets.entries) {
      GamePiece piece = entry.key;
      int bet = entry.value;
      
      int count = diceResults.where((dice) => dice == piece).length;
      if (count > 0) {
        win += bet * count;
      } else {
        lose += bet;
      }
    }
    
    return win - lose;
  }
  
  int _calculateAIScoreChange() {
    int win = 0;
    int lose = 0;
    
    for (var entry in aiBets.entries) {
      GamePiece piece = entry.key;
      int bet = entry.value;
      
      int count = diceResults.where((dice) => dice == piece).length;
      if (count > 0) {
        win += bet * count;
      } else {
        lose += bet;
      }
    }
    
    return win - lose;
  }
  
  void _checkGameEnd() {
    if (playerScore <= 0 || aiScore <= 0) {
      isGameActive = false;
    }
  }
  
  // Lấy người thắng
  CoCaNguaPlayer? getWinner() {
    if (isGameActive) return null;
    
    if (playerScore > aiScore) return CoCaNguaPlayer.player;
    if (aiScore > playerScore) return CoCaNguaPlayer.ai;
    return null; // Hòa
  }
  
  // Reset game
  void resetGame() {
    playerScore = 100;
    aiScore = 100;
    gameHistory.clear();
    _initializeGame();
  }
  
  // Lấy tổng tiền cược của người chơi
  int getTotalPlayerBets() {
    return playerBets.values.fold(0, (sum, bet) => sum + bet);
  }
  
  // Kiểm tra có thể đặt cược không
  bool canPlaceBet(int amount) {
    return getTotalPlayerBets() + amount <= playerScore;
  }
}

class GameRound {
  final List<GamePiece> diceResults;
  final Map<GamePiece, int> playerBets;
  final Map<GamePiece, int> aiBets;
  final int playerScoreChange;
  final int aiScoreChange;
  
  GameRound({
    required this.diceResults,
    required this.playerBets,
    required this.aiBets,
    required this.playerScoreChange,
    required this.aiScoreChange,
  });
}

extension GamePieceExtension on GamePiece {
  String get displayName {
    switch (this) {
      case GamePiece.fish:
        return 'Cá';
      case GamePiece.crab:
        return 'Cua';
      case GamePiece.shrimp:
        return 'Tôm';
      case GamePiece.rooster:
        return 'Gà';
      case GamePiece.gourd:
        return 'Bầu';
      case GamePiece.deer:
        return 'Nai';
    }
  }
  
  String get emoji {
    switch (this) {
      case GamePiece.fish:
        return '🐟';
      case GamePiece.crab:
        return '🦀';
      case GamePiece.shrimp:
        return '🦐';
      case GamePiece.rooster:
        return '🐓';
      case GamePiece.gourd:
        return '🎃';
      case GamePiece.deer:
        return '🦌';
    }
  }
}
