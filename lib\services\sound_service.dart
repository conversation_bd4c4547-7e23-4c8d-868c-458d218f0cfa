import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SoundService {
  static final SoundService _instance = SoundService._internal();
  factory SoundService() => _instance;
  SoundService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _soundEnabled = true;
  bool _musicEnabled = true;
  double _soundVolume = 0.7;
  double _musicVolume = 0.5;

  // Initialize sound service
  Future<void> initialize() async {
    await _loadSettings();
  }

  // Load sound settings from SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _soundEnabled = prefs.getBool('sound_enabled') ?? true;
    _musicEnabled = prefs.getBool('music_enabled') ?? true;
    _soundVolume = prefs.getDouble('sound_volume') ?? 0.7;
    _musicVolume = prefs.getDouble('music_volume') ?? 0.5;
  }

  // Save sound settings to SharedPreferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('music_enabled', _musicEnabled);
    await prefs.setDouble('sound_volume', _soundVolume);
    await prefs.setDouble('music_volume', _musicVolume);
  }

  // Getters
  bool get soundEnabled => _soundEnabled;
  bool get musicEnabled => _musicEnabled;
  double get soundVolume => _soundVolume;
  double get musicVolume => _musicVolume;

  // Setters
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
  }

  Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    await _saveSettings();
    if (!enabled) {
      await _audioPlayer.stop();
    }
  }

  Future<void> setSoundVolume(double volume) async {
    _soundVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
  }

  Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
  }

  // Play sound effects
  Future<void> playSound(SoundEffect effect) async {
    if (!_soundEnabled) return;

    try {
      await _audioPlayer.play(
        AssetSource(_getSoundPath(effect)),
        volume: _soundVolume,
      );
    } catch (e) {
      // Fallback: create simple beep sound programmatically
      // For now, we'll just ignore the error since we don't have actual sound files
      print('Sound not available: ${effect.name}');
    }
  }

  // Play background music
  Future<void> playBackgroundMusic() async {
    if (!_musicEnabled) return;

    try {
      await _audioPlayer.play(
        AssetSource('sounds/background_music.mp3'),
        volume: _musicVolume,
      );
      await _audioPlayer.setReleaseMode(ReleaseMode.loop);
    } catch (e) {
      print('Background music not available');
    }
  }

  // Stop background music
  Future<void> stopBackgroundMusic() async {
    await _audioPlayer.stop();
  }

  // Get sound file path
  String _getSoundPath(SoundEffect effect) {
    switch (effect) {
      case SoundEffect.buttonClick:
        return 'sounds/button_click.wav';
      case SoundEffect.gameStart:
        return 'sounds/game_start.wav';
      case SoundEffect.gameWin:
        return 'sounds/game_win.wav';
      case SoundEffect.gameLose:
        return 'sounds/game_lose.wav';
      case SoundEffect.pieceMove:
        return 'sounds/piece_move.wav';
      case SoundEffect.pieceCapture:
        return 'sounds/piece_capture.wav';
      case SoundEffect.diceRoll:
        return 'sounds/dice_roll.wav';
      case SoundEffect.coinDrop:
        return 'sounds/coin_drop.wav';
      case SoundEffect.success:
        return 'sounds/success.wav';
      case SoundEffect.error:
        return 'sounds/error.wav';
    }
  }

  // Dispose resources
  void dispose() {
    _audioPlayer.dispose();
  }
}

enum SoundEffect {
  buttonClick,
  gameStart,
  gameWin,
  gameLose,
  pieceMove,
  pieceCapture,
  diceRoll,
  coinDrop,
  success,
  error,
}
