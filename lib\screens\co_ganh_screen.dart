import 'package:flutter/material.dart';
import '../models/co_ganh_game.dart';
import '../services/co_ganh_ai.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';

class CoGanhScreen extends StatefulWidget {
  const CoGanhScreen({super.key});

  @override
  State<CoGanhScreen> createState() => _CoGanhScreenState();
}

class _CoGanhScreenState extends State<CoGanhScreen> {
  late CoGanhGame game;
  late CoGanhAI ai;
  bool isAIThinking = false;
  Position? selectedPosition;

  @override
  void initState() {
    super.initState();
    game = CoGanhGame();
    ai = CoGanhAI(difficulty: CoGanhAIDifficulty.medium);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cờ Gánh'),
        backgroundColor: AppColors.primaryRed,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetGame,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.background, AppColors.creamWhite],
          ),
        ),
        child: Column(
          children: [
            _buildScoreBoard(),
            Expanded(
              child: _buildGameBoard(),
            ),
            _buildGameControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreBoard() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: AppColors.primaryGold.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildPlayerScore(
            'Bạn', 
            game.player1Pieces, 
            game.currentPlayer == CoGanhPlayer.player1,
            AppColors.success,
          ),
          _buildPlayerScore(
            'Máy', 
            game.player2Pieces, 
            game.currentPlayer == CoGanhPlayer.player2,
            AppColors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerScore(String name, int pieces, bool isCurrentPlayer, Color color) {
    return Column(
      children: [
        Text(
          name,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isCurrentPlayer ? AppColors.primaryRed : AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isCurrentPlayer ? AppColors.primaryRed : color,
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          ),
          child: Text(
            '$pieces quân',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGameBoard() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.darkBrown,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: AspectRatio(
        aspectRatio: 1.0,
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: CoGanhGame.boardSize,
            crossAxisSpacing: 2,
            mainAxisSpacing: 2,
          ),
          itemCount: CoGanhGame.boardSize * CoGanhGame.boardSize,
          itemBuilder: (context, index) {
            int row = index ~/ CoGanhGame.boardSize;
            int col = index % CoGanhGame.boardSize;
            Position pos = Position(row, col);
            
            return _buildCell(pos);
          },
        ),
      ),
    );
  }

  Widget _buildCell(Position pos) {
    PieceType piece = game.board[pos.row][pos.col];
    bool isSelected = selectedPosition == pos;
    bool canMove = _canSelectPosition(pos);
    
    return GestureDetector(
      onTap: () => _onCellTapped(pos),
      child: Container(
        decoration: BoxDecoration(
          color: _getCellColor(pos, piece, isSelected),
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          border: Border.all(
            color: isSelected ? AppColors.primaryRed : AppColors.lightBrown,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Center(
          child: _buildPiece(piece, canMove),
        ),
      ),
    );
  }

  Color _getCellColor(Position pos, PieceType piece, bool isSelected) {
    if (isSelected) return AppColors.primaryGold;
    
    // Màu ô cờ xen kẽ
    bool isDark = (pos.row + pos.col) % 2 == 1;
    return isDark ? AppColors.lightBrown : AppColors.creamWhite;
  }

  Widget _buildPiece(PieceType piece, bool canMove) {
    switch (piece) {
      case PieceType.player1:
        return Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: AppColors.success,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: canMove ? [
              BoxShadow(
                color: AppColors.success.withValues(alpha: 0.5),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ] : null,
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 20,
          ),
        );
      case PieceType.player2:
        return Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: AppColors.error,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: const Icon(
            Icons.smart_toy,
            color: Colors.white,
            size: 20,
          ),
        );
      case PieceType.empty:
        return const SizedBox();
    }
  }

  Widget _buildGameControls() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          if (!game.isGameActive)
            _buildGameOverMessage(),
          
          if (isAIThinking)
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('AI đang suy nghĩ...'),
              ],
            ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _resetGame,
                child: const Text('Chơi lại'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Thoát'),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingSmall),
          
          Text(
            selectedPosition != null 
                ? 'Đã chọn: (${selectedPosition!.row}, ${selectedPosition!.col})'
                : game.currentPlayer == CoGanhPlayer.player1 
                    ? 'Lượt của bạn - Chọn quân để di chuyển'
                    : 'Lượt của máy',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGameOverMessage() {
    CoGanhPlayer? winner = game.getWinner();
    String message;
    Color color;
    
    if (winner == CoGanhPlayer.player1) {
      message = 'Chúc mừng! Bạn đã thắng!';
      color = AppColors.success;
    } else if (winner == CoGanhPlayer.player2) {
      message = 'AI đã thắng! Thử lại nhé!';
      color = AppColors.error;
    } else {
      message = 'Hòa! Trận đấu cân bằng!';
      color = AppColors.warning;
    }
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: color),
      ),
      child: Text(
        message,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  bool _canSelectPosition(Position pos) {
    if (!game.isGameActive || isAIThinking) return false;
    if (game.currentPlayer != CoGanhPlayer.player1) return false;
    
    PieceType piece = game.board[pos.row][pos.col];
    return piece == PieceType.player1;
  }

  void _onCellTapped(Position pos) {
    if (!game.isGameActive || isAIThinking) return;
    if (game.currentPlayer != CoGanhPlayer.player1) return;
    
    setState(() {
      if (selectedPosition == null) {
        // Chọn quân
        if (game.board[pos.row][pos.col] == PieceType.player1) {
          selectedPosition = pos;
        }
      } else {
        // Di chuyển quân
        if (game.makeMove(selectedPosition!, pos)) {
          selectedPosition = null;
          
          // AI turn
          if (game.currentPlayer == CoGanhPlayer.player2 && game.isGameActive) {
            _makeAIMove();
          }
        } else {
          // Chọn quân khác hoặc bỏ chọn
          if (game.board[pos.row][pos.col] == PieceType.player1) {
            selectedPosition = pos;
          } else {
            selectedPosition = null;
          }
        }
      }
    });
  }

  void _makeAIMove() async {
    setState(() {
      isAIThinking = true;
    });
    
    await Future.delayed(const Duration(milliseconds: 1000));
    
    Move? aiMove = ai.getBestMove(game);
    if (aiMove != null) {
      game.makeMove(aiMove.from, aiMove.to);
    }
    
    setState(() {
      isAIThinking = false;
    });
  }

  void _resetGame() {
    setState(() {
      game.resetGame();
      isAIThinking = false;
      selectedPosition = null;
    });
  }
}
