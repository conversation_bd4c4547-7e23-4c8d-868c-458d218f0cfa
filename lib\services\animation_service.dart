import 'package:flutter/material.dart';

class AnimationService {
  static final AnimationService _instance = AnimationService._internal();
  factory AnimationService() => _instance;
  AnimationService._internal();

  // Tạo animation cho button press
  static Widget createButtonPressAnimation({
    required Widget child,
    required VoidCallback onPressed,
    Duration duration = const Duration(milliseconds: 100),
  }) {
    return _AnimatedButton(
      onPressed: onPressed,
      duration: duration,
      child: child,
    );
  }

  // Tạo animation cho piece movement
  static Widget createPieceMovementAnimation({
    required Widget child,
    required bool isMoving,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: Curves.easeInOut,
      transform: Matrix4.identity()
        ..scale(isMoving ? 1.1 : 1.0)
        ..translate(0.0, isMoving ? -5.0 : 0.0),
      child: child,
    );
  }

  // Tạo animation cho score change
  static Widget createScoreChangeAnimation({
    required Widget child,
    required bool hasChanged,
    Duration duration = const Duration(milliseconds: 500),
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: Curves.elasticOut,
      transform: Matrix4.identity()..scale(hasChanged ? 1.2 : 1.0),
      child: child,
    );
  }

  // Tạo animation cho game over
  static Widget createGameOverAnimation({
    required Widget child,
    required bool isGameOver,
    Duration duration = const Duration(milliseconds: 800),
  }) {
    return AnimatedOpacity(
      opacity: isGameOver ? 1.0 : 0.0,
      duration: duration,
      curve: Curves.easeInOut,
      child: AnimatedContainer(
        duration: duration,
        curve: Curves.bounceOut,
        transform: Matrix4.identity()
          ..scale(isGameOver ? 1.0 : 0.0)
          ..translate(0.0, isGameOver ? 0.0 : 50.0),
        child: child,
      ),
    );
  }

  // Tạo animation cho card flip
  static Widget createCardFlipAnimation({
    required Widget front,
    required Widget back,
    required bool showFront,
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return _FlipCard(
      front: front,
      back: back,
      showFront: showFront,
      duration: duration,
    );
  }

  // Tạo animation cho slide transition
  static Widget createSlideTransition({
    required Widget child,
    required bool isVisible,
    SlideDirection direction = SlideDirection.fromBottom,
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return AnimatedSlide(
      offset: isVisible ? Offset.zero : _getSlideOffset(direction),
      duration: duration,
      curve: Curves.easeInOut,
      child: child,
    );
  }

  // Tạo animation cho fade transition
  static Widget createFadeTransition({
    required Widget child,
    required bool isVisible,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return AnimatedOpacity(
      opacity: isVisible ? 1.0 : 0.0,
      duration: duration,
      curve: Curves.easeInOut,
      child: child,
    );
  }

  // Tạo animation cho pulse effect
  static Widget createPulseAnimation({
    required Widget child,
    required bool isPulsing,
    Duration duration = const Duration(milliseconds: 1000),
  }) {
    return _PulseAnimation(
      child: child,
      isPulsing: isPulsing,
      duration: duration,
    );
  }

  static Offset _getSlideOffset(SlideDirection direction) {
    switch (direction) {
      case SlideDirection.fromTop:
        return const Offset(0, -1);
      case SlideDirection.fromBottom:
        return const Offset(0, 1);
      case SlideDirection.fromLeft:
        return const Offset(-1, 0);
      case SlideDirection.fromRight:
        return const Offset(1, 0);
    }
  }
}

enum SlideDirection {
  fromTop,
  fromBottom,
  fromLeft,
  fromRight,
}

// Custom animated button widget
class _AnimatedButton extends StatefulWidget {
  final Widget child;
  final VoidCallback onPressed;
  final Duration duration;

  const _AnimatedButton({
    required this.child,
    required this.onPressed,
    required this.duration,
  });

  @override
  State<_AnimatedButton> createState() => _AnimatedButtonState();
}

class _AnimatedButtonState extends State<_AnimatedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onPressed();
      },
      onTapCancel: () => _controller.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
}

// Custom flip card widget
class _FlipCard extends StatefulWidget {
  final Widget front;
  final Widget back;
  final bool showFront;
  final Duration duration;

  const _FlipCard({
    required this.front,
    required this.back,
    required this.showFront,
    required this.duration,
  });

  @override
  State<_FlipCard> createState() => _FlipCardState();
}

class _FlipCardState extends State<_FlipCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _flipAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (!widget.showFront) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(_FlipCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showFront != oldWidget.showFront) {
      if (widget.showFront) {
        _controller.reverse();
      } else {
        _controller.forward();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _flipAnimation,
      builder: (context, child) {
        final isShowingFront = _flipAnimation.value < 0.5;
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(_flipAnimation.value * 3.14159),
          child: isShowingFront ? widget.front : widget.back,
        );
      },
    );
  }
}

// Custom pulse animation widget
class _PulseAnimation extends StatefulWidget {
  final Widget child;
  final bool isPulsing;
  final Duration duration;

  const _PulseAnimation({
    required this.child,
    required this.isPulsing,
    required this.duration,
  });

  @override
  State<_PulseAnimation> createState() => _PulseAnimationState();
}

class _PulseAnimationState extends State<_PulseAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isPulsing) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(_PulseAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPulsing != oldWidget.isPulsing) {
      if (widget.isPulsing) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: widget.child,
        );
      },
    );
  }
}
