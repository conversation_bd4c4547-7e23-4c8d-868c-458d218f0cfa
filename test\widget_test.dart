// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:traditional_game/main.dart';

void main() {
  testWidgets('App loads home screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const TraditionalGameApp());

    // Verify that the app title is displayed
    expect(find.text('<PERSON><PERSON>'), findsOneWidget);

    // Verify that game selection text is displayed
    expect(find.text('Chọn trò chơi yêu thích của bạn'), findsOneWidget);

    // Verify that at least one game card is displayed
    expect(find.text('Ô Ăn Quan'), findsOneWidget);
  });
}
