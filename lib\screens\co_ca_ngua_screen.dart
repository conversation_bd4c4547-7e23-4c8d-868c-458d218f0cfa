import 'package:flutter/material.dart';
import '../models/co_ca_ngua_game.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';

class CoCaNguaScreen extends StatefulWidget {
  const CoCaNguaScreen({super.key});

  @override
  State<CoCaNguaScreen> createState() => _CoCaNguaScreenState();
}

class _CoCaNguaScreenState extends State<CoCaNguaScreen> with TickerProviderStateMixin {
  late CoCaNguaGame game;
  late AnimationController _diceAnimationController;
  late AnimationController _resultAnimationController;
  bool isRolling = false;

  @override
  void initState() {
    super.initState();
    game = CoCaNguaGame();
    _diceAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _resultAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _diceAnimationController.dispose();
    _resultAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cờ Cá Ngựa'),
        backgroundColor: AppColors.primaryRed,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetGame,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.background, AppColors.creamWhite],
          ),
        ),
        child: Column(
          children: [
            _buildScoreBoard(),
            _buildDiceArea(),
            Expanded(
              child: _buildBettingArea(),
            ),
            _buildGameControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreBoard() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: AppColors.primaryGold.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildPlayerScore('Bạn', game.playerScore, AppColors.success),
          _buildPlayerScore('Máy', game.aiScore, AppColors.error),
        ],
      ),
    );
  }

  Widget _buildPlayerScore(String name, int score, Color color) {
    return Column(
      children: [
        Text(
          name,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          ),
          child: Text(
            score.toString(),
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDiceArea() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.darkBrown,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Kết quả xúc xắc',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              for (int i = 0; i < 3; i++)
                _buildDice(i),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDice(int index) {
    return AnimatedBuilder(
      animation: _diceAnimationController,
      builder: (context, child) {
        return Transform.rotate(
          angle: _diceAnimationController.value * 4 * 3.14159,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.creamWhite,
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              border: Border.all(color: AppColors.primaryGold, width: 2),
            ),
            child: Center(
              child: Text(
                game.diceResults.length > index 
                    ? game.diceResults[index].emoji
                    : '?',
                style: const TextStyle(fontSize: 30),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBettingArea() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Đặt cược (Tổng: ${game.getTotalPlayerBets()})',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: AppConstants.paddingMedium,
                mainAxisSpacing: AppConstants.paddingMedium,
              ),
              itemCount: GamePiece.values.length,
              itemBuilder: (context, index) {
                GamePiece piece = GamePiece.values[index];
                return _buildBettingCard(piece);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBettingCard(GamePiece piece) {
    int currentBet = game.playerBets[piece] ?? 0;
    bool hasWon = game.diceResults.contains(piece);
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        side: BorderSide(
          color: hasWon ? AppColors.success : AppColors.primaryGold.withOpacity(0.3),
          width: hasWon ? 3 : 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          gradient: hasWon 
              ? LinearGradient(
                  colors: [AppColors.success.withOpacity(0.1), AppColors.success.withOpacity(0.05)],
                )
              : null,
        ),
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              piece.emoji,
              style: const TextStyle(fontSize: 32),
            ),
            Text(
              piece.displayName,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Cược: $currentBet',
              style: TextStyle(
                fontSize: 12,
                color: currentBet > 0 ? AppColors.primaryRed : AppColors.textSecondary,
                fontWeight: currentBet > 0 ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildBetButton('-', () => _decreaseBet(piece)),
                _buildBetButton('+', () => _increaseBet(piece)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBetButton(String label, VoidCallback onPressed) {
    return SizedBox(
      width: 30,
      height: 30,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          backgroundColor: AppColors.primaryRed,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          ),
        ),
        child: Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildGameControls() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          if (!game.isGameActive)
            _buildGameOverMessage(),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: game.isGameActive && game.getTotalPlayerBets() > 0 && !isRolling
                    ? _rollDice
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: Text(
                  isRolling ? 'Đang lắc...' : 'Lắc xúc xắc',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              ElevatedButton(
                onPressed: _clearAllBets,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text(
                  'Xóa cược',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              ElevatedButton(
                onPressed: _resetGame,
                child: const Text('Chơi lại'),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingSmall),
          
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.textSecondary,
            ),
            child: const Text(
              'Thoát',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameOverMessage() {
    CoCaNguaPlayer? winner = game.getWinner();
    String message;
    Color color;
    
    if (winner == CoCaNguaPlayer.player) {
      message = 'Chúc mừng! Bạn đã thắng!';
      color = AppColors.success;
    } else if (winner == CoCaNguaPlayer.ai) {
      message = 'Máy đã thắng! Thử lại nhé!';
      color = AppColors.error;
    } else {
      message = 'Hòa! Trận đấu cân bằng!';
      color = AppColors.warning;
    }
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: color),
      ),
      child: Text(
        message,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  void _increaseBet(GamePiece piece) {
    if (game.canPlaceBet(10)) {
      setState(() {
        game.placeBet(piece, 10);
      });
    }
  }

  void _decreaseBet(GamePiece piece) {
    int currentBet = game.playerBets[piece] ?? 0;
    if (currentBet >= 10) {
      setState(() {
        game.playerBets[piece] = currentBet - 10;
        if (game.playerBets[piece] == 0) {
          game.playerBets.remove(piece);
        }
      });
    }
  }

  void _clearAllBets() {
    setState(() {
      game.clearAllBets();
    });
  }

  void _rollDice() async {
    setState(() {
      isRolling = true;
    });
    
    _diceAnimationController.forward();
    
    await Future.delayed(const Duration(milliseconds: 1500));
    
    setState(() {
      game.rollDice();
      isRolling = false;
    });
    
    _diceAnimationController.reset();
    _resultAnimationController.forward().then((_) {
      _resultAnimationController.reset();
    });
  }

  void _resetGame() {
    setState(() {
      game.resetGame();
      isRolling = false;
    });
    _diceAnimationController.reset();
    _resultAnimationController.reset();
  }
}
