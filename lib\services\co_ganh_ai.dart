import 'dart:math';
import '../models/co_ganh_game.dart';

enum CoGanhAIDifficulty { easy, medium, hard }

class CoGanhAI {
  final CoGanhAIDifficulty difficulty;
  final Random _random = Random();
  
  CoGanhAI({this.difficulty = CoGanhAIDifficulty.medium});
  
  // Chọn nước đi tốt nhất
  Move? getBestMove(CoGanhGame game) {
    List<Move> validMoves = game.getValidMoves();
    
    if (validMoves.isEmpty) return null;
    
    switch (difficulty) {
      case CoGanhAIDifficulty.easy:
        return _getRandomMove(validMoves);
      case CoGanhAIDifficulty.medium:
        return _getMediumMove(game, validMoves);
      case CoGanhAIDifficulty.hard:
        return _getHardMove(game, validMoves);
    }
  }
  
  // AI dễ: chọn ngẫu nhiên
  Move _getRandomMove(List<Move> validMoves) {
    return validMoves[_random.nextInt(validMoves.length)];
  }
  
  // AI trung bình: ưu tiên gánh và tr<PERSON>h bị gánh
  Move _getMediumMove(CoGanhGame game, List<Move> validMoves) {
    Move bestMove = validMoves[0];
    int bestScore = -1000;
    
    for (Move move in validMoves) {
      int score = _evaluateMove(game, move);
      
      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }
    
    return bestMove;
  }
  
  // AI khó: sử dụng minimax
  Move _getHardMove(CoGanhGame game, List<Move> validMoves) {
    Move bestMove = validMoves[0];
    int bestScore = -10000;
    
    for (Move move in validMoves) {
      CoGanhGame testGame = game.copy();
      testGame.makeMove(move.from, move.to);
      
      int score = _minimax(testGame, 3, false, -10000, 10000);
      
      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }
    
    return bestMove;
  }
  
  // Đánh giá nước đi
  int _evaluateMove(CoGanhGame game, Move move) {
    CoGanhGame testGame = game.copy();
    
    int initialPlayer2Pieces = testGame.player2Pieces;
    int initialPlayer1Pieces = testGame.player1Pieces;
    
    testGame.makeMove(move.from, move.to);
    
    int capturedPieces = initialPlayer1Pieces - testGame.player1Pieces;
    int positionValue = _getPositionValue(move.to);
    int safetyValue = _getSafetyValue(testGame, move.to);
    
    return capturedPieces * 100 + positionValue * 10 + safetyValue;
  }
  
  // Giá trị vị trí (ưu tiên trung tâm)
  int _getPositionValue(Position pos) {
    int centerDistance = (pos.row - 2).abs() + (pos.col - 2).abs();
    return 4 - centerDistance; // Càng gần trung tâm càng tốt
  }
  
  // Đánh giá độ an toàn của vị trí
  int _getSafetyValue(CoGanhGame game, Position pos) {
    int safetyScore = 0;
    PieceType myPiece = game.board[pos.row][pos.col];
    PieceType enemyPiece = myPiece == PieceType.player1 
        ? PieceType.player2 
        : PieceType.player1;
    
    // Kiểm tra các vị trí xung quanh
    List<List<int>> directions = [
      [-1, 0], [1, 0], [0, -1], [0, 1],
      [-1, -1], [-1, 1], [1, -1], [1, 1],
    ];
    
    for (List<int> dir in directions) {
      Position adjacent = Position(pos.row + dir[0], pos.col + dir[1]);
      
      if (_isInBoard(adjacent)) {
        PieceType adjacentPiece = game.board[adjacent.row][adjacent.col];
        
        if (adjacentPiece == myPiece) {
          safetyScore += 5; // Có quân bạn bên cạnh
        } else if (adjacentPiece == enemyPiece) {
          safetyScore -= 3; // Có quân địch bên cạnh
        }
      }
    }
    
    return safetyScore;
  }
  
  // Minimax với alpha-beta pruning
  int _minimax(CoGanhGame game, int depth, bool isMaximizing, int alpha, int beta) {
    if (depth == 0 || !game.isGameActive) {
      return _evaluateGameState(game);
    }
    
    List<Move> validMoves = game.getValidMoves();
    
    if (isMaximizing) {
      int maxScore = -10000;
      
      for (Move move in validMoves) {
        CoGanhGame testGame = game.copy();
        testGame.makeMove(move.from, move.to);
        
        int score = _minimax(testGame, depth - 1, false, alpha, beta);
        maxScore = max(maxScore, score);
        alpha = max(alpha, score);
        
        if (beta <= alpha) break; // Alpha-beta pruning
      }
      
      return maxScore;
    } else {
      int minScore = 10000;
      
      for (Move move in validMoves) {
        CoGanhGame testGame = game.copy();
        testGame.makeMove(move.from, move.to);
        
        int score = _minimax(testGame, depth - 1, true, alpha, beta);
        minScore = min(minScore, score);
        beta = min(beta, score);
        
        if (beta <= alpha) break; // Alpha-beta pruning
      }
      
      return minScore;
    }
  }
  
  // Đánh giá trạng thái game
  int _evaluateGameState(CoGanhGame game) {
    if (!game.isGameActive) {
      CoGanhPlayer? winner = game.getWinner();
      if (winner == CoGanhPlayer.player2) return 1000;
      if (winner == CoGanhPlayer.player1) return -1000;
      return 0; // Hòa
    }
    
    // Đánh giá dựa trên số quân và vị trí
    int pieceDiff = game.player2Pieces - game.player1Pieces;
    int positionValue = _evaluateAllPositions(game);
    int mobilityValue = _evaluateMobility(game);
    
    return pieceDiff * 50 + positionValue + mobilityValue;
  }
  
  // Đánh giá tất cả vị trí quân cờ
  int _evaluateAllPositions(CoGanhGame game) {
    int totalValue = 0;
    
    for (int row = 0; row < CoGanhGame.boardSize; row++) {
      for (int col = 0; col < CoGanhGame.boardSize; col++) {
        Position pos = Position(row, col);
        PieceType piece = game.board[row][col];
        
        if (piece == PieceType.player2) {
          totalValue += _getPositionValue(pos);
        } else if (piece == PieceType.player1) {
          totalValue -= _getPositionValue(pos);
        }
      }
    }
    
    return totalValue;
  }
  
  // Đánh giá khả năng di chuyển
  int _evaluateMobility(CoGanhGame game) {
    CoGanhPlayer originalPlayer = game.currentPlayer;
    
    // Đếm nước đi của AI
    game.currentPlayer = CoGanhPlayer.player2;
    int aiMoves = game.getValidMoves().length;
    
    // Đếm nước đi của người chơi
    game.currentPlayer = CoGanhPlayer.player1;
    int playerMoves = game.getValidMoves().length;
    
    game.currentPlayer = originalPlayer;
    
    return (aiMoves - playerMoves) * 2;
  }
  
  bool _isInBoard(Position pos) {
    return pos.row >= 0 && pos.row < CoGanhGame.boardSize && 
           pos.col >= 0 && pos.col < CoGanhGame.boardSize;
  }
}
