import '../models/game_info.dart';

class AppConstants {
  static const String appName = 'Dân Gian T<PERSON>';
  static const String appSubtitle = 'Tr<PERSON> chơi dân gian truyền thống Việt Nam';
  
  // Danh sách các game
  static const List<GameInfo> availableGames = [
    GameInfo(
      id: 'o_an_quan',
      name: 'Ô Ăn Quan',
      description: 'Tr<PERSON> chơi trí tuệ truyền thống với chiến thuật sâu sắc',
      imagePath: 'assets/images/o_an_quan.png',
      difficulty: GameDifficulty.medium,
      maxPlayers: 2,
    ),
    GameInfo(
      id: 'co_ca_ngua',
      name: '<PERSON><PERSON>á Ng<PERSON>',
      description: '<PERSON>r<PERSON> chơi may rủi với xúc xắc, phù hợp mọi lứa tuổi',
      imagePath: 'assets/images/co_ca_ngua.png',
      difficulty: GameDifficulty.easy,
      maxPlayers: 4,
    ),
    GameInfo(
      id: 'co_ganh',
      name: '<PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON> chơi chiến thuật với luật chơi độc đáo',
      imagePath: 'assets/images/co_ganh.png',
      difficulty: GameDifficulty.medium,
      maxPlayers: 2,
    ),
  ];
  
  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Border radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
}
