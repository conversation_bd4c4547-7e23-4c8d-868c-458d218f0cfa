import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../services/sound_service.dart';
import '../services/animation_service.dart';

class FeedbackWidget extends StatefulWidget {
  final String message;
  final FeedbackType type;
  final Duration duration;
  final VoidCallback? onComplete;

  const FeedbackWidget({
    super.key,
    required this.message,
    required this.type,
    this.duration = const Duration(seconds: 2),
    this.onComplete,
  });

  @override
  State<FeedbackWidget> createState() => _FeedbackWidgetState();
}

class _FeedbackWidgetState extends State<FeedbackWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -100.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _startAnimation();
  }

  void _startAnimation() async {
    // Play sound effect
    await SoundService().playSound(_getSoundEffect());
    
    // Start animation
    await _controller.forward();
    
    // Wait for duration
    await Future.delayed(widget.duration);
    
    // Fade out
    await _controller.reverse();
    
    // Call completion callback
    if (widget.onComplete != null) {
      widget.onComplete!();
    }
  }

  SoundEffect _getSoundEffect() {
    switch (widget.type) {
      case FeedbackType.success:
        return SoundEffect.success;
      case FeedbackType.error:
        return SoundEffect.error;
      case FeedbackType.info:
        return SoundEffect.buttonClick;
      case FeedbackType.warning:
        return SoundEffect.error;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(AppConstants.paddingMedium),
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
                vertical: AppConstants.paddingMedium,
              ),
              decoration: BoxDecoration(
                color: _getBackgroundColor(),
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getIcon(),
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Flexible(
                    child: Text(
                      widget.message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getBackgroundColor() {
    switch (widget.type) {
      case FeedbackType.success:
        return AppColors.success;
      case FeedbackType.error:
        return AppColors.error;
      case FeedbackType.warning:
        return AppColors.warning;
      case FeedbackType.info:
        return AppColors.textSecondary;
    }
  }

  IconData _getIcon() {
    switch (widget.type) {
      case FeedbackType.success:
        return Icons.check_circle;
      case FeedbackType.error:
        return Icons.error;
      case FeedbackType.warning:
        return Icons.warning;
      case FeedbackType.info:
        return Icons.info;
    }
  }
}

enum FeedbackType {
  success,
  error,
  warning,
  info,
}

// Utility class to show feedback messages
class FeedbackManager {
  static OverlayEntry? _currentOverlay;

  static void showFeedback(
    BuildContext context, {
    required String message,
    required FeedbackType type,
    Duration duration = const Duration(seconds: 2),
  }) {
    // Remove existing feedback if any
    _currentOverlay?.remove();

    // Create new overlay
    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 20,
        left: 20,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: FeedbackWidget(
            message: message,
            type: type,
            duration: duration,
            onComplete: () {
              _currentOverlay?.remove();
              _currentOverlay = null;
            },
          ),
        ),
      ),
    );

    // Insert overlay
    Overlay.of(context).insert(_currentOverlay!);
  }

  static void showSuccess(BuildContext context, String message) {
    showFeedback(
      context,
      message: message,
      type: FeedbackType.success,
    );
  }

  static void showError(BuildContext context, String message) {
    showFeedback(
      context,
      message: message,
      type: FeedbackType.error,
    );
  }

  static void showWarning(BuildContext context, String message) {
    showFeedback(
      context,
      message: message,
      type: FeedbackType.warning,
    );
  }

  static void showInfo(BuildContext context, String message) {
    showFeedback(
      context,
      message: message,
      type: FeedbackType.info,
    );
  }

  static void clear() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
}
