import 'package:flutter/material.dart';
import '../models/game_info.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../widgets/game_card.dart';
import '../services/sound_service.dart';
import '../services/animation_service.dart';
import '../widgets/feedback_widget.dart';
import 'o_an_quan_screen.dart';
import 'co_ca_ngua_screen.dart';
import 'co_ganh_screen.dart';
import 'settings_screen.dart';
import 'statistics_screen.dart';
import 'instructions_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              AppColors.creamWhite,
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              _buildHeader(context),
              Expanded(
                child: _buildGameGrid(context),
              ),
              _buildFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        children: [
          // App title with traditional decoration
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingLarge,
              vertical: AppConstants.paddingMedium,
            ),
            decoration: BoxDecoration(
              gradient: AppColors.redGradient,
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
              boxShadow: [
                BoxShadow(
                  color: AppColors.darkBrown.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Text(
                  AppConstants.appName,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  AppConstants.appSubtitle,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Welcome message
          Text(
            'Chọn trò chơi yêu thích của bạn',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameGrid(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          childAspectRatio: 1.8,
          mainAxisSpacing: AppConstants.paddingMedium,
        ),
        itemCount: AppConstants.availableGames.length,
        itemBuilder: (context, index) {
          final game = AppConstants.availableGames[index];
          return GameCard(
            gameInfo: game,
            onTap: () => _onGameSelected(context, game),
          );
        },
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildFooterButton(
            context,
            icon: Icons.settings,
            label: 'Cài đặt',
            onTap: () => _showSettings(context),
          ),
          _buildFooterButton(
            context,
            icon: Icons.info_outline,
            label: 'Hướng dẫn',
            onTap: () => _showInstructions(context),
          ),
          _buildFooterButton(
            context,
            icon: Icons.star,
            label: 'Thành tích',
            onTap: () => _showAchievements(context),
          ),
        ],
      ),
    );
  }

  Widget _buildFooterButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          border: Border.all(
            color: AppColors.primaryGold.withOpacity(0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: AppColors.textSecondary),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onGameSelected(BuildContext context, GameInfo game) {
    // Play button click sound
    SoundService().playSound(SoundEffect.buttonClick);

    switch (game.id) {
      case 'o_an_quan':
        SoundService().playSound(SoundEffect.gameStart);
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const OAnQuanScreen(),
          ),
        );
        break;
      case 'co_ca_ngua':
        SoundService().playSound(SoundEffect.gameStart);
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const CoCaNguaScreen(),
          ),
        );
        break;
      case 'co_ganh':
        SoundService().playSound(SoundEffect.gameStart);
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const CoGanhScreen(),
          ),
        );
        break;
      default:
        FeedbackManager.showInfo(context, 'Đang khởi động ${game.name}...');
    }
  }

  void _showSettings(BuildContext context) {
    SoundService().playSound(SoundEffect.buttonClick);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  void _showInstructions(BuildContext context) {
    SoundService().playSound(SoundEffect.buttonClick);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const InstructionsScreen(),
      ),
    );
  }

  void _showAchievements(BuildContext context) {
    SoundService().playSound(SoundEffect.buttonClick);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const StatisticsScreen(),
      ),
    );
  }


}
