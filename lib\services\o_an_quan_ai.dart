import 'dart:math';
import '../models/o_an_quan_game.dart';

enum AIDifficulty { easy, medium, hard }

class OAnQuanAI {
  final AIDifficulty difficulty;
  final Random _random = Random();
  
  OAnQuanAI({this.difficulty = AIDifficulty.medium});
  
  // Chọn nước đi tốt nhất
  int getBestMove(OAnQuanGame game) {
    List<int> validMoves = game.getValidMoves();
    
    if (validMoves.isEmpty) return -1;
    
    switch (difficulty) {
      case AIDifficulty.easy:
        return _getRandomMove(validMoves);
      case AIDifficulty.medium:
        return _getMediumMove(game, validMoves);
      case AIDifficulty.hard:
        return _getHardMove(game, validMoves);
    }
  }
  
  // AI dễ: chọn ngẫu nhiên
  int _getRandomMove(List<int> validMoves) {
    return validMoves[_random.nextInt(validMoves.length)];
  }
  
  // AI trung bình: ưu tiên ăn quân và tránh để đối thủ ăn
  int _getMediumMove(OAnQuanGame game, List<int> validMoves) {
    int bestMove = validMoves[0];
    int bestScore = -1000;
    
    for (int move in validMoves) {
      int score = _evaluateMove(game, move);
      
      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }
    
    return bestMove;
  }
  
  // AI khó: sử dụng minimax đơn giản
  int _getHardMove(OAnQuanGame game, List<int> validMoves) {
    int bestMove = validMoves[0];
    int bestScore = -10000;
    
    for (int move in validMoves) {
      // Tạo bản sao game để test
      OAnQuanGame testGame = _copyGame(game);
      testGame.makeMove(move);
      
      int score = _minimax(testGame, 2, false);
      
      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }
    
    return bestMove;
  }
  
  // Đánh giá nước đi
  int _evaluateMove(OAnQuanGame game, int move) {
    // Tạo bản sao game để test
    OAnQuanGame testGame = _copyGame(game);
    int initialScore = testGame.player2Score;
    
    testGame.makeMove(move);
    
    int scoreGain = testGame.player2Score - initialScore;
    int positionValue = _getPositionValue(move);
    
    return scoreGain * 10 + positionValue;
  }
  
  // Giá trị vị trí (ưu tiên các ô có nhiều quân)
  int _getPositionValue(int position) {
    // Ưu tiên các ô ở giữa
    if (position == 8 || position == 9) return 3;
    if (position == 7 || position == 10) return 2;
    return 1;
  }
  
  // Minimax algorithm đơn giản
  int _minimax(OAnQuanGame game, int depth, bool isMaximizing) {
    if (depth == 0 || game.status == GameStatus.gameOver) {
      return _evaluateGameState(game);
    }
    
    List<int> validMoves = game.getValidMoves();
    
    if (isMaximizing) {
      int maxScore = -10000;
      for (int move in validMoves) {
        OAnQuanGame testGame = _copyGame(game);
        testGame.makeMove(move);
        int score = _minimax(testGame, depth - 1, false);
        maxScore = max(maxScore, score);
      }
      return maxScore;
    } else {
      int minScore = 10000;
      for (int move in validMoves) {
        OAnQuanGame testGame = _copyGame(game);
        testGame.makeMove(move);
        int score = _minimax(testGame, depth - 1, true);
        minScore = min(minScore, score);
      }
      return minScore;
    }
  }
  
  // Đánh giá trạng thái game
  int _evaluateGameState(OAnQuanGame game) {
    if (game.status == GameStatus.gameOver) {
      Player? winner = game.getWinner();
      if (winner == Player.player2) return 1000;
      if (winner == Player.player1) return -1000;
      return 0; // Hòa
    }
    
    // Đánh giá dựa trên điểm số và số quân còn lại
    int scoreDiff = game.player2Score - game.player1Score;
    int stoneDiff = _countStones(game, Player.player2) - _countStones(game, Player.player1);
    
    return scoreDiff * 5 + stoneDiff;
  }
  
  // Đếm số quân của một người chơi
  int _countStones(OAnQuanGame game, Player player) {
    int count = 0;
    int start = player == Player.player1 ? 0 : 6;
    int end = player == Player.player1 ? 6 : 12;
    
    for (int i = start; i < end; i++) {
      count += game.board[i];
    }
    
    return count;
  }
  
  // Tạo bản sao game
  OAnQuanGame _copyGame(OAnQuanGame original) {
    OAnQuanGame copy = OAnQuanGame();
    copy.board = List.from(original.board);
    copy.quan1 = original.quan1;
    copy.quan2 = original.quan2;
    copy.currentPlayer = original.currentPlayer;
    copy.status = original.status;
    copy.player1Score = original.player1Score;
    copy.player2Score = original.player2Score;
    return copy;
  }
}
